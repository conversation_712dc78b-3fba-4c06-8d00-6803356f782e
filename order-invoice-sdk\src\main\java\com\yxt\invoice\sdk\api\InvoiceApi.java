package com.yxt.invoice.sdk.api;

import com.yxt.invoice.sdk.dto.req.ApplyInvoiceMainReqDto;
import com.yxt.invoice.sdk.dto.req.ApplyRedInvoiceMainReqDto;
import com.yxt.invoice.sdk.dto.res.ApplyInvoiceResDto;
import com.yxt.invoice.sdk.dto.res.ApplyRedCreditResDto;
import com.yxt.lang.dto.api.ResponseBase;

import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 发票服务SDK接口
 * 为医药电商平台提供开票相关服务
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public interface InvoiceApi {



    /**
     * 申请开票
     *
     * @param reqDto 开票申请请求
     * @return 开票申请结果
     */
    @PostMapping("/apply")
    @ApiOperation(value = "申请发票")
    ResponseBase<List<ApplyInvoiceResDto>> apply(@Valid @RequestBody ApplyInvoiceMainReqDto reqDto);




    /**
     * 申请开票
     * 
     * @param reqDto 开票申请请求
     * @return 开票申请结果
     */
    @ApiOperation(value = "申请红票")
    @PostMapping("/apply/red")
    ResponseBase<ApplyRedCreditResDto> applyRed(@Valid @RequestBody ApplyRedInvoiceMainReqDto reqDto);


}
