package com.yxt.invoice.interfaces.service.impl;

import com.yxt.invoice.application.service.InvoiceApplicationService;
import com.yxt.invoice.domain.command.*;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.interfaces.service.InvoiceService;
import com.yxt.lang.dto.api.PageDTO;
import lombok.extern.slf4j.Slf4j;


import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发票服务实现
 * Service层，使用Command传参，委托给Application层处理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Service
@Slf4j
public class InvoiceServiceImpl implements InvoiceService {


    @Resource
    private InvoiceApplicationService invoiceApplicationService;



    @Override
    public List<InvoiceAggregate> applyInvoice(ApplyInvoiceCommand command) {
        command.validateApplyInvoice();

        return invoiceApplicationService.applyInvoice(command);
    }

    @Override
    public InvoiceAggregate applyRedCreditInvoice(RedCreditInvoiceCommand command) {
        return invoiceApplicationService.applyRedCreditInvoice(command);
    }

    @Override
    public ExistsOrderInvoice queryOrderExistsInvoice(ExistsOrderInvoiceCommand command) {


        return invoiceApplicationService.queryOrderExistsInvoice(command);
    }

    @Override
    public ExistsOrderInvoice queryThirdOrderExistsInvoiceReqDto(ExistsThirdOrderInvoiceCommand command) {

        return invoiceApplicationService.queryThirdOrderExistsInvoiceReqDto(command);
    }

    @Override
    public PageDTO<InvoiceMain> queryInvoiceList(QueryInvoiceListCommand query) {
        // 委托给Application层处理，获取InvoiceMain列表
        return invoiceApplicationService.queryInvoiceList(query);
    }

    @Override
    public InvoiceAggregate queryInvoiceDetail(QueryInvoiceDetailCommand query) {
        log.info("Service层处理发票详情查询，开票单号：{}", query.getInvoiceMainNo());



        // 委托给Application层处理
        return invoiceApplicationService.queryInvoiceDetail(query);
    }



//    @Override
//    public InvoiceAggregate applyInvoice(ApplyInvoiceCommand command) {
//        log.info("Service层处理申请开票，平台订单号:{} 订单号：{}", command.getInvoiceMain().getThirdOrderNo().getThirdOrderNo(), command.getInvoiceMain().getOrderNo().getOrderNo());
//        // 委托给Application层处理
//        return invoiceApplicationService.applyInvoice(command);
//    }







}
