package com.yxt.invoice.interfaces.converter;

import com.google.common.base.Preconditions;
import com.yxt.invoice.domain.command.*;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.invoice.infrastructure.common.UserContext;
import com.yxt.invoice.sdk.dto.InvoiceAmountDTO;
import com.yxt.invoice.sdk.dto.req.*;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * 发票请求转换器
 * 负责将SDK请求对象转换为Domain Command或Application Query
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Slf4j
public class InvoiceRequestConverter {



    /**
     * 转换申请开票请求为命令
     */
    public static ApplyInvoiceCommand convertToApplyInvoiceCommand(ApplyInvoiceReqDto request) {
        log.debug("开始转换申请开票请求为命令");

        if (request == null) {
            throw new IllegalArgumentException("申请开票请求不能为空");
        }


        String currentUser = UserContext.getCurrentUserId();

        ApplyInvoiceCommand command = new ApplyInvoiceCommand();
        // 转换DTO为Domain对象
        command.setInvoiceMain(InvoiceDTOConverter.convertToInvoiceMain(request.getInvoiceMain()));
        command.setDetails(InvoiceDTOConverter.convertToInvoiceDetails(request.getDetails()));



        log.debug("申请开票请求转换完成，订单号：{}", command.getInvoiceMain().getOrderNo());
        return command;
    }

    /**
     * 转换红冲请求为命令
     */
    public static RedCreditInvoiceCommand convertToRedCreditCommand(ApplyRedCreditReqDto request) {
        log.debug("开始转换红冲请求为命令");

        if (request == null) {
            throw new IllegalArgumentException("红冲请求不能为空");
        }


        String currentUser = UserContext.getCurrentUserId();

        RedCreditInvoiceCommand command = new RedCreditInvoiceCommand();



        command.setInvoiceMainNo(request.getInvoiceMainNo());
        command.setRedCreditReason(request.getRedCreditReason());
        command.setNotes(request.getNotes());
        command.setOperatorUserId(currentUser);
        command.setApplyTime(request.getApplyTime());


        log.debug("红冲请求转换完成，开票单号：{}", command.getInvoiceMainNo());
        return command;
    }



    /**
     * 转换发票详情请求为查询对象
     */
    public static QueryInvoiceDetailCommand convertToInvoiceDetailCommand(InvoiceDetailReqDto request) {
        log.debug("开始转换发票详情请求为查询对象");

        if (request == null) {
            throw new IllegalArgumentException("详情查询请求不能为空");
        }
        QueryInvoiceDetailCommand command = new QueryInvoiceDetailCommand();
        command.setInvoiceMainNo(request.getInvoiceMainNo());

        return command;
    }



    public static ApplyInvoiceCommand convertToSimpleApplyInvoiceCommand(
        ApplyInvoiceMainReqDto reqDto) {
        ApplyInvoiceCommand command = new ApplyInvoiceCommand();

        InvoiceMain invoiceMain = InvoiceMain.simpleApplyInvoice(reqDto.getOrderNo(),reqDto.getMerCode(),reqDto.getBusinessType(),reqDto.getTransactionChannel()
                ,reqDto.getOperatorUserId(),reqDto.getUserId(),reqDto.getInvoiceType(),reqDto.getNotes(),reqDto.getApplyTime()
                ,reqDto.getApplyChannel(),reqDto.getBuyerPartyType(),reqDto.getBuyerName(),
                reqDto.getBuyerTin(),reqDto.getBuyerAddress(),reqDto.getBuyerPhone(),reqDto.getBuyerBank()
                ,reqDto.getBuyerBankAccount(),reqDto.getBuyerEmail(),reqDto.getBuyerMobile()
                ,reqDto.getShowBuyerBankAccount());
        command.setInvoiceMain(invoiceMain);
        command.setInvoiceAmount(convertToInvoiceAmount(reqDto.getInvoiceAmount()));
        String userId = UserContext.getCurrentUserId();
        if(Objects.isNull(userId)){
            throw new RuntimeException("请登录");
        }
        log.info("userId:{} operatorUserId:{}",userId,reqDto.getOperatorUserId());
        Preconditions.checkArgument(userId.equals(reqDto.getOperatorUserId()), "当前用户ID与操作用户ID不一致");
        return command;
    }


    private static InvoiceAmount convertToInvoiceAmount(InvoiceAmountDTO reqDto) {
        InvoiceAmount invoiceAmount  = new InvoiceAmount();
        invoiceAmount.setKey(reqDto.getKey());
        invoiceAmount.setAmount(reqDto.getAmount());
        return invoiceAmount;
    }
    public static RedCreditInvoiceCommand convertToSimpleRedCreditCommand(
        ApplyRedInvoiceMainReqDto reqDto) {
        RedCreditInvoiceCommand command = new RedCreditInvoiceCommand();
        command.setInvoiceMainNo(reqDto.getRedInvoiceMainNo());
        command.setRedCreditReason(reqDto.getRedInvoiceReason());
        command.setNotes(reqDto.getNotes());
        command.setOperatorUserId(reqDto.getOperatorUserId());
        command.setApplyTime(reqDto.getApplyTime());
        return command;

    }

    public static ExistsOrderInvoiceCommand convertToExistsOrderInvoiceCommand(QueryOrderExistsInvoiceReqDto reqDto) {
        ExistsOrderInvoiceCommand command = new ExistsOrderInvoiceCommand();
        command.setOrderNo(reqDto.getOrderNo());
        command.setTransactionChannel(reqDto.getTransactionChannel());
        command.setBusinessType(reqDto.getBusinessType());
        command.setPosNo(reqDto.getPosNo());
        return command;
    }

    public static ExistsThirdOrderInvoiceCommand convertToExistsThirdOrderInvoiceCommand(QueryThirdOrderExistsInvoiceReqDto reqDto) {
        ExistsThirdOrderInvoiceCommand command = new ExistsThirdOrderInvoiceCommand();
        command.setThirdOrderNo(reqDto.getThirdOrderNo());
        return command;
    }


}
