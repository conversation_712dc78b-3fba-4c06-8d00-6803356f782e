package com.yxt.invoice.interfaces.controller;

import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.command.ExistsOrderInvoiceCommand;
import com.yxt.invoice.domain.command.ExistsThirdOrderInvoiceCommand;
import com.yxt.invoice.domain.command.RedCreditInvoiceCommand;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.interfaces.converter.InvoiceDTOConverter;
import com.yxt.invoice.interfaces.converter.InvoiceRequestConverter;
import com.yxt.invoice.interfaces.service.InvoiceService;
import com.yxt.invoice.sdk.api.InvoiceApi;
import com.yxt.invoice.sdk.api.InvoiceQueryApi;
import com.yxt.invoice.sdk.dto.req.QueryOrderExistsInvoiceReqDto;
import com.yxt.invoice.sdk.dto.req.QueryThirdOrderExistsInvoiceReqDto;
import com.yxt.invoice.sdk.dto.req.ApplyInvoiceMainReqDto;
import com.yxt.invoice.sdk.dto.req.ApplyRedInvoiceMainReqDto;
import com.yxt.invoice.sdk.dto.res.ApplyInvoiceResDto;
import com.yxt.invoice.sdk.dto.res.ApplyRedCreditResDto;
import com.yxt.invoice.sdk.dto.res.QueryOrderExistsInvoiceResDto;
import com.yxt.lang.dto.api.ResponseBase;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发票控制器 实现SDK接口，提供RESTful API
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@ApiOperation(value = "核心接口")
@RestController
@RequestMapping("/api/1.0/invoice")
@Slf4j
public class InvoiceController implements InvoiceApi, InvoiceQueryApi {

  @Resource
  private InvoiceService invoiceService;



  @Override
  public ResponseBase<List<ApplyInvoiceResDto>> apply(
      ApplyInvoiceMainReqDto reqDto) {

    try {
      // 转换为Command
      ApplyInvoiceCommand command = InvoiceRequestConverter.convertToSimpleApplyInvoiceCommand(
          reqDto);

      // 调用Service层，返回聚合根
      List<InvoiceAggregate> aggregateList = invoiceService.applyInvoice(command);
      List<ApplyInvoiceResDto> resDtoList = new ArrayList<>();
      for (InvoiceAggregate invoiceAggregate : aggregateList) {
        // 转换聚合根为响应DTO
        resDtoList.add(InvoiceDTOConverter.convertToApplyInvoiceResDto(invoiceAggregate));
      }
      log.info("申请开票成功，开票单号：{}", resDtoList.get(0).getInvoiceMain().getInvoiceMainNo());
      return ResponseBase.success(resDtoList);

    } catch (Exception e) {
      log.error("申请开票系统错误：{}", e.getMessage(), e);
      return ResponseBase.fail("SYSTEM_ERROR", e.getMessage());
    }
  }



  @Override
  public ResponseBase<ApplyRedCreditResDto> applyRed(
      ApplyRedInvoiceMainReqDto reqDto) {

    try {
      // 转换为Command
      RedCreditInvoiceCommand command = InvoiceRequestConverter.convertToSimpleRedCreditCommand(
          reqDto);

      // 调用Service层，返回聚合根
      InvoiceAggregate aggregate = invoiceService.applyRedCreditInvoice(command);

      // 转换聚合根为响应DTO
      ApplyRedCreditResDto result = InvoiceDTOConverter.convertToApplyRedCreditResDto(aggregate);

      log.info("发票红冲成功，开票单号：{}", result.getInvoiceMainNo());
      return ResponseBase.success(result);

    } catch (Exception e) {
      log.error("发票红冲系统错误：{}", e.getMessage(), e);
      return ResponseBase.fail("SYSTEM_ERROR", e.getMessage());
    }
  }

  @Override
  public ResponseBase<QueryOrderExistsInvoiceResDto> queryOrderExistsInvoice(
      QueryOrderExistsInvoiceReqDto req) {
    ExistsOrderInvoiceCommand command = InvoiceRequestConverter.convertToExistsOrderInvoiceCommand(
        req);
    ExistsOrderInvoice existsInvoice = invoiceService.queryOrderExistsInvoice(command);

    QueryOrderExistsInvoiceResDto resDto = InvoiceDTOConverter.convertToExistsOrderInvoice(
        existsInvoice);
    return ResponseBase.success(resDto);
  }

  @Override
  public ResponseBase<QueryOrderExistsInvoiceResDto> queryThirdOrderExistsInvoiceReqDto(
      QueryThirdOrderExistsInvoiceReqDto req) {
    ExistsThirdOrderInvoiceCommand command = InvoiceRequestConverter.convertToExistsThirdOrderInvoiceCommand(
        req);
    ExistsOrderInvoice existsInvoice = invoiceService.queryThirdOrderExistsInvoiceReqDto(command);

    QueryOrderExistsInvoiceResDto resDto = InvoiceDTOConverter.convertToExistsOrderInvoice(
        existsInvoice);
    return ResponseBase.success(resDto);
  }

//    @PostMapping("/applyInvoice")
//    @Override
//    public ResponseBase<ApplyInvoiceResDto> applyInvoice(@Valid @RequestBody ApplyInvoiceReqDto reqDto) {
//        log.info("收到申请开票请求，平台订单号：{}, 订单号：{}", reqDto.getInvoiceMain().getThirdOrderNo(), reqDto.getInvoiceMain().getOrderNo());
//
//        try {
//            // 转换为Command
//            ApplyInvoiceCommand command = InvoiceRequestConverter.convertToApplyInvoiceCommand(reqDto);
//
//            // 调用Service层，返回聚合根
//            InvoiceAggregate aggregate = invoiceService.applyInvoice(command);
//
//            // 转换聚合根为响应DTO
//            ApplyInvoiceResDto result = InvoiceDTOConverter.convertToApplyInvoiceResDto(aggregate);
//
//            log.info("申请开票成功，开票单号：{}", result.getInvoiceMain().getInvoiceMainNo());
//            return ResponseBase.success(result);
//
//        } catch (IllegalArgumentException e) {
//            log.warn("申请开票参数错误：{}", e.getMessage());
//            return ResponseBase.fail("INVALID_PARAM", e.getMessage());
//
//        } catch (IllegalStateException e) {
//            log.warn("申请开票状态错误：{}", e.getMessage());
//            return ResponseBase.fail("INVALID_STATE", e.getMessage());
//
//        } catch (Exception e) {
//            log.error("申请开票系统错误：{}", e.getMessage(), e);
//            return ResponseBase.fail("SYSTEM_ERROR", "系统繁忙，请稍后重试");
//        }
//    }


}
