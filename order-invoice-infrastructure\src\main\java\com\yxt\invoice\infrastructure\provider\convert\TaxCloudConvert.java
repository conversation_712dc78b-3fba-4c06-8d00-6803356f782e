package com.yxt.invoice.infrastructure.provider.convert;

import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.utils.OrderDateUtils;
import com.yxt.invoice.infrastructure.provider.dto.req.GetInvoiceByResponseIdReqDto;
import com.yxt.invoice.infrastructure.provider.dto.req.PositiveInvoiceIssueReqDto;
import com.yxt.invoice.infrastructure.provider.dto.req.PostNegativeInvoiceIssueReqDto;
import com.yxt.order.types.invoice.enums.InvoiceBuyerPartyTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceLineTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTypeEnum;

import java.util.List;
import java.util.stream.Collectors;


public class TaxCloudConvert {

    public static PositiveInvoiceIssueReqDto.InvoiceData convertToPositiveInvoiceIssueReqDto(InvoiceAggregate aggregate) {

        InvoiceMain invoiceMain = aggregate.getInvoiceMain();
        PositiveInvoiceIssueReqDto.InvoiceData reqDto = getInvoiceData(invoiceMain);
        reqDto.setItemList(convertToInvoiceItemList(aggregate.getInvoiceDetailList()));

        return reqDto;
    }

    private static PositiveInvoiceIssueReqDto.InvoiceData getInvoiceData(InvoiceMain invoiceMain) {
        PositiveInvoiceIssueReqDto.InvoiceData reqDto = new PositiveInvoiceIssueReqDto.InvoiceData();

        reqDto.setOutRequestCode(invoiceMain.getInvoiceMainNo());
        reqDto.setInTaxRateTag("Y");
        reqDto.setInvoiceType(invoiceMain.getInvoiceType().equals(InvoiceTypeEnum.ORDINARY_INVOICE) ? "01" : "02");
        reqDto.setBuyerType(invoiceMain.getBuyerPartyType().equals(InvoiceBuyerPartyTypeEnum.INDIVIDUAL) ? "Y" : "N");
        reqDto.setBuyerName(invoiceMain.getBuyerName());
        reqDto.setBuyerTin(invoiceMain.getBuyerTin());
        reqDto.setBuyerAddress(invoiceMain.getBuyerAddress());
        reqDto.setBuyerPhone(invoiceMain.getBuyerPhone());
        reqDto.setBuyerBank(invoiceMain.getBuyerBank());
        reqDto.setBuyerBankAccount(invoiceMain.getBuyerBankAccount());
        reqDto.setBuyerEmail(invoiceMain.getBuyerEmail());
        reqDto.setBuyerMobile(invoiceMain.getBuyerMobile());
        reqDto.setShowBuyerBankAccount(invoiceMain.getShowBuyerBankAccount());
//        reqDto.setRealitySellerName(invoiceMain.getSellerName());
//        reqDto.setRealitySellerNumber(invoiceMain.getSellerNumber());
        reqDto.setSellerNumber(invoiceMain.getSellerNumber());
//        reqDto.setSellerTin(invoiceMain.getSellerTin());
//        reqDto.setSellerName(invoiceMain.getSellerName());
//        reqDto.setSellerAddress(invoiceMain.getSellerAddress());
//        reqDto.setSellerPhone(invoiceMain.getSellerPhone());
//        reqDto.setSellerBank(invoiceMain.getSellerBank());
//        reqDto.setSellerBankAccount(invoiceMain.getSellerBankAccount());
        reqDto.setOperator(invoiceMain.getOperator());
        reqDto.setRequestDate(OrderDateUtils.formatYYMMDD(invoiceMain.getApplyTime()));
        reqDto.setInvoiceAmount(invoiceMain.getInvoiceAmount());
        reqDto.setTaxAmount(invoiceMain.getTaxAmount());
        reqDto.setPriceTaxAmount(invoiceMain.getPriceTaxAmount());
        reqDto.setSplitBill(invoiceMain.getSplitBill());
        reqDto.setNotes("");
        reqDto.setPayee(invoiceMain.getPayee());
        reqDto.setReviewedBy(invoiceMain.getReviewed());
        reqDto.setSpecificElements("");
        return reqDto;
    }

    private static List<PositiveInvoiceIssueReqDto.InvoiceItem> convertToInvoiceItemList(List<InvoiceDetail> invoiceDetailList) {
        return invoiceDetailList.stream().map(TaxCloudConvert::convertToInvoiceItem).collect(Collectors.toList());
    }

    private static PositiveInvoiceIssueReqDto.InvoiceItem convertToInvoiceItem(InvoiceDetail invoiceDetail) {
        PositiveInvoiceIssueReqDto.InvoiceItem item = new PositiveInvoiceIssueReqDto.InvoiceItem();
        String lineType = "00";
        if (invoiceDetail.getInvoiceLineType().equals(InvoiceLineTypeEnum.REGULAR_LINE.getCode())) {
            lineType = "00";
        } else if (invoiceDetail.getInvoiceLineType().equals(InvoiceLineTypeEnum.DISCOUNT_LINE.getCode())) {
            lineType = "01";
        } else if (invoiceDetail.getInvoiceLineType().equals(InvoiceLineTypeEnum.DISCOUNTED_LINE.getCode())) {
            lineType = "02";
        }
        item.setLine(Long.valueOf(invoiceDetail.getRowNo()));
        item.setInvoiceLineType(lineType);
        item.setInvoiceQty(invoiceDetail.getCommodityCount());
        item.setItemPrice(invoiceDetail.getPrice().toPlainString());
        item.setMaterialId(invoiceDetail.getErpCode());
        item.setAmount(invoiceDetail.getTotalAmount());
        item.setInTaxAmount(invoiceDetail.getPriceTaxAmount());
        item.setTaxAmount(invoiceDetail.getTaxAmount());
        item.setTaxRate(invoiceDetail.getTaxRate());
        item.setFlag("");
        return item;
    }


    public static PostNegativeInvoiceIssueReqDto.NegativeInvoiceData convertToPostNegativeInvoiceIssueReqDto(  InvoiceAggregate redInvoiceAggregate) {



        PostNegativeInvoiceIssueReqDto.NegativeInvoiceData reqDto = new PostNegativeInvoiceIssueReqDto.NegativeInvoiceData();
        reqDto.setSellerNumber(redInvoiceAggregate.getInvoiceMain().getSellerNumber());
        reqDto.setOutRequestCode(redInvoiceAggregate.getInvoiceMain().getInvoiceMainNo());
        reqDto.setOriginalOutRequestCode(redInvoiceAggregate.getInvoiceMain().getRedInvoiceMainNo());
        reqDto.setWriteOffReason(redInvoiceAggregate.getInvoiceMain().getRedInvoiceReason());
        reqDto.setIsEntire("Y");
        return reqDto;

    }


    public static GetInvoiceByResponseIdReqDto convertToGetInvoiceByResponseIdReqDto(InvoiceAggregate aggregate) {
        GetInvoiceByResponseIdReqDto reqDto = new GetInvoiceByResponseIdReqDto();
        reqDto.setOutRequestCode(aggregate.getInvoiceMain().getInvoiceMainNo());
        reqDto.setInvoiceTag(aggregate.getInvoiceMain().getInvoiceRedBlueType().equals(InvoiceRedBlueTypeEnum.TAX_INVOICE) ? "0" : "1");
        return reqDto;
    }
}
