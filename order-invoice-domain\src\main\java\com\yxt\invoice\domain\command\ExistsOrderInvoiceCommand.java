package com.yxt.invoice.domain.command;

import com.yxt.order.types.offline.OfflineThirdOrderNo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发票详情查询
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
@NoArgsConstructor
public class ExistsOrderInvoiceCommand  {



    /**
     * 订单号
     */
    @ApiModelProperty("订单号,必填")
    private String orderNo;


    /**
     * 交易场景 ONLINE:线上交易, OFFLINE:线下交易
     */
    @ApiModelProperty("交易场景 ONLINE:线上交易, OFFLINE:线下交易,必填")
    private String transactionChannel;

    /**
     * 业务类型 O2O, B2C
     */
    @ApiModelProperty("业务类型 O2O、B2C, 线下单填O2O , 必填 ")
    private String businessType;

    /**
     * posNo
     */
    @ApiModelProperty("线下交易时必填")
    private String posNo;


    /**
     * 平台编码
     */
    @ApiModelProperty("平台编码,必填")
    private String thirdPlatformCode;

    /**
     * 第三方平台订单号
     */
    @ApiModelProperty("三方订单号,必填")
    private OfflineThirdOrderNo thirdOrderNo;

    /**
     * 门店编码
     */
    @ApiModelProperty("门店编码,必填")
    private String storeCode;


}
