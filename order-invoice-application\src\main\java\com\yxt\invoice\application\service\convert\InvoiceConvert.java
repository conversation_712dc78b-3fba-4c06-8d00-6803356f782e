package com.yxt.invoice.application.service.convert;


import com.google.common.base.Preconditions;
import com.yxt.domain.order.order_query.res.B2cOmsOrderInfo;
import com.yxt.domain.order.order_query.res.B2cOrderAllDetailRes;
import com.yxt.domain.order.order_query.res.B2cOrderDetail;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedRes;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedResInvoice;
import com.yxt.domain.order.refund_query.res.B2cRefundAllDetailRes;
import com.yxt.domain.order.refund_query.res.B2cRefundDetail;
import com.yxt.domain.order.refund_query.res.RefundDomainRelatedRes;
import com.yxt.invoice.application.third.goods.dto.res.AveragePriceVO;
import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.order.common.base_order_dto.OrderDetail;
import com.yxt.order.common.base_order_dto.OrderInfo;
import com.yxt.order.common.base_order_dto.RefundDetail;
import com.yxt.order.common.utils.OrderDateUtil;
import com.yxt.order.open.message.offline_order.model.OfflineOrderModel;
import com.yxt.order.open.message.offline_order.model.OfflineRefundOrderModel;
import com.yxt.order.types.invoice.enums.InvoiceDeliveryTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceLineTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceSyncStatusEnum;
import com.yxt.order.types.offline.OfflineOrderNo;
import com.yxt.order.types.offline.OfflineThirdOrderNo;

import java.math.BigDecimal;
import java.math.RoundingMode;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

public class InvoiceConvert {


    public static ApplyInvoiceCommand applyInvoiceConvertByOfflineOrder(OfflineOrderModel data, List<OfflineRefundOrderModel> refundDataList, ApplyInvoiceCommand command, List<AveragePriceVO> detailPriceList) {
        OfflineOrderModel.OfflineOrderInfo baseOrderInfo = data.getBaseOrderInfo();
        OfflineOrderModel.Organization baseOrganizationInfo = data.getBaseOrganizationInfo();
        InvoiceMain invoiceMain = command.getInvoiceMain();
        Date applyTime = invoiceMain.getApplyTime();
        String createdBy = invoiceMain.getCreatedBy();

        BigDecimal sumDetailInvoiceAmount = BigDecimal.ZERO;
        BigDecimal sumDetailTaxAmount = BigDecimal.ZERO;


        List<InvoiceAmount> invoiceAmounts = InvoiceAmountConvert.convertByOfflineOrder(data, refundDataList);
        Optional<InvoiceAmount> first = invoiceAmounts.stream().filter(invoiceAmount -> invoiceAmount.getKey().equals(command.getInvoiceAmount().getKey())).findFirst();
        Preconditions.checkArgument(first.isPresent(), "请选择发票金额开具方式");
        BigDecimal invoiceAmount = first.get().getAmount();
        BigDecimal actualPayAmount = invoiceAmount;

        Map<String, AveragePriceVO> priceVOMap = detailPriceList.stream().collect(Collectors.toMap(AveragePriceVO::getErpCode, d -> d));
        Map<String, List<OfflineRefundOrderModel.RefundDetail>> mapByRefundDetailList = refundDataList.stream().flatMap(model -> model.getRefundDetailList().stream()).collect(Collectors.groupingBy(OfflineRefundOrderModel.RefundDetail::getRowNo));


        List<InvoiceDetail> invoiceDetails = new ArrayList<>();
        for (OfflineOrderModel.OrderDetail orderDetail : data.getOrderDetailList()) {
            BigDecimal commodityCount = orderDetail.getBaseOrderDetailInfo().getCommodityCount();
            String rowNo = orderDetail.getBaseOrderDetailInfo().getRowNo();
            List<OfflineRefundOrderModel.RefundDetail> refundDetails = mapByRefundDetailList.get(rowNo);
            if (!refundDetails.isEmpty()) {
                for (OfflineRefundOrderModel.RefundDetail refundDetail : refundDetails) {
                    commodityCount = commodityCount.subtract(refundDetail.getRefundCount());
                }
            }
            if (commodityCount.intValue() <= 0) {
                continue;
            }
            AveragePriceVO averagePriceVO = priceVOMap.get(orderDetail.getBaseOrderDetailInfo().getErpCode().getErpCode());
            BigDecimal taxRate = new BigDecimal(averagePriceVO.getTaxRate());
            BigDecimal price = orderDetail.getBaseOrderDetailInfo().getPrice();
            BigDecimal totalAmount = price.multiply(commodityCount);
            BigDecimal taxAmount = totalAmount.multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
            BigDecimal PriceTaxAmount = totalAmount.add(taxAmount);

            sumDetailInvoiceAmount = sumDetailInvoiceAmount.add(totalAmount);
            sumDetailTaxAmount = sumDetailTaxAmount.add(taxAmount);


            InvoiceDetail invoiceDetail = new InvoiceDetail();
            invoiceDetail.setRowNo(rowNo);
            invoiceDetail.setErpCode(orderDetail.getBaseOrderDetailInfo().getErpCode().getErpCode());
            invoiceDetail.setErpName(orderDetail.getBaseOrderDetailInfo().getErpName());
            invoiceDetail.setCommodityCount(commodityCount);
            invoiceDetail.setPrice(price);
            invoiceDetail.setTotalAmount(totalAmount);
            invoiceDetail.setTaxAmount(taxAmount);
            invoiceDetail.setTaxRate(taxRate);
            invoiceDetail.setPriceTaxAmount(PriceTaxAmount);
            invoiceDetail.setInvoiceLineType(InvoiceLineTypeEnum.REGULAR_LINE.getCode());
            invoiceDetail.setDiscountAmount(BigDecimal.ZERO);
            invoiceDetail.setPolicyStatus("NO");
            invoiceDetail.setPolicyTaxRate(null);
            invoiceDetail.setIsValid(1L);
            invoiceDetail.setCreated(applyTime);
            invoiceDetail.setUpdated(applyTime);
            invoiceDetail.setCreatedBy(createdBy);
            invoiceDetail.setUpdatedBy(createdBy);
            invoiceDetail.setVersion(1L);
            invoiceDetails.add(invoiceDetail);
        }

        Preconditions.checkArgument(invoiceAmount.doubleValue() != sumDetailInvoiceAmount.doubleValue(), "发票头与商品行合计金额不一致");
        BigDecimal priceTaxAmount = sumDetailInvoiceAmount.add(sumDetailTaxAmount);

        invoiceMain.setCompanyCode(baseOrganizationInfo.getCompanyCode());
        invoiceMain.setCompanyName(baseOrganizationInfo.getCompanyName());
        invoiceMain.setOrganizationCode(baseOrganizationInfo.getStoreCode());
        invoiceMain.setOrganizationName(baseOrganizationInfo.getStoreName());
        invoiceMain.setThirdPlatformCode(baseOrderInfo.getThirdPlatformCodeValue());
        invoiceMain.setThirdOrderNo(baseOrderInfo.getThirdOrderNo());
        invoiceMain.setOrderNo(baseOrderInfo.getOrderNo());
        invoiceMain.setPosNo(baseOrderInfo.getThirdOrderNo().getThirdOrderNo());
        invoiceMain.setInvoiceStatus(InvoiceStatusEnum.WAIT);
        invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.WAIT);
        invoiceMain.setActualPayAmount(actualPayAmount);
        invoiceMain.setDeliveryAmount(BigDecimal.ZERO);
        invoiceMain.setDeliveryType(InvoiceDeliveryTypeEnum.SELF_PICK_UP.getCode());
        invoiceMain.setInvoiceAmount(invoiceAmount);
        invoiceMain.setTaxAmount(sumDetailTaxAmount);
        invoiceMain.setPriceTaxAmount(priceTaxAmount);
        invoiceMain.setSplitBill("NOT");
        invoiceMain.setOrderCreated(baseOrderInfo.getCreated());
        invoiceMain.setSellerNumber(baseOrganizationInfo.getStoreCode());
        invoiceMain.setSellerName(baseOrganizationInfo.getStoreName());
        invoiceMain.setIsValid(1L);
        invoiceMain.setCreated(new Date());
        invoiceMain.setUpdated(new Date());
        invoiceMain.setVersion(1L);

        command.setInvoiceMain(invoiceMain);
        command.setDetails(invoiceDetails);
        return command;
    }


    public static ApplyInvoiceCommand applyInvoiceConvertByOnlineOrderO2O(
        OrderDomainRelatedResInvoice data, List<RefundDomainRelatedRes> refundDataList, ApplyInvoiceCommand command, List<AveragePriceVO> detailPriceList) {
        OrderInfo baseOrderInfo = data.getOrderInfo();
        String deliveryTypeTemp = data.getOrderDeliveryRecord().getDeliveryType();
        InvoiceMain invoiceMain = command.getInvoiceMain();
        Date applyTime = invoiceMain.getApplyTime();
        String createdBy = invoiceMain.getCreatedBy();
        String deliveryType;
        if (deliveryTypeTemp.equals("3")) {
            deliveryType = InvoiceDeliveryTypeEnum.SELF_PICK_UP.getCode();
        } else if (deliveryTypeTemp.equals("1") || deliveryTypeTemp.equals("2")) {
            deliveryType = InvoiceDeliveryTypeEnum.PLATFORM_FULFILLMENT.getCode();
        } else {
            deliveryType = InvoiceDeliveryTypeEnum.MERCHANT_FULFILLMENT.getCode();

        }
        BigDecimal sumDetailInvoiceAmount = BigDecimal.ZERO;
        BigDecimal sumDetailTaxAmount = BigDecimal.ZERO;

        List<InvoiceAmount> invoiceAmounts = InvoiceAmountConvert.convertByOnlineOrderO2O(data, refundDataList);
        Optional<InvoiceAmount> first = invoiceAmounts.stream().filter(invoiceAmount -> invoiceAmount.getKey().equals(command.getInvoiceAmount().getKey())).findFirst();
        Preconditions.checkArgument(first.isPresent(), "请选择发票金额开具方式");
        BigDecimal invoiceAmount = first.get().getAmount();

        Map<String, AveragePriceVO> priceVOMap = detailPriceList.stream().collect(Collectors.toMap(AveragePriceVO::getErpCode, d -> d));
        Map<String, List<RefundDetail>> mapByRefundDetailList = refundDataList.stream().flatMap(model -> model.getRefundDetailList().stream()).collect(Collectors.groupingBy(RefundDetail::getThirdDetailId));


        List<InvoiceDetail> invoiceDetails = new ArrayList<>();
        for (OrderDetail orderDetail : data.getDetailList()) {
            if (orderDetail.getStatus() != 0) {
                continue;
            }
            Integer commodityCount = orderDetail.getCommodityCount();
            String rowNo = orderDetail.getThirdDetailId();
            List<RefundDetail> refundDetails = mapByRefundDetailList.get(rowNo);
            if (!CollectionUtils.isEmpty(refundDetails)) {
                for (RefundDetail refundDetail : refundDetails) {
                    commodityCount = commodityCount - (refundDetail.getRefundCount());
                }
            }
            if (commodityCount <= 0) {
                continue;
            }
            AveragePriceVO averagePriceVO = priceVOMap.get(orderDetail.getErpCode());
            BigDecimal taxRate = new BigDecimal(averagePriceVO.getTaxRate());
            BigDecimal price = orderDetail.getPrice();
            BigDecimal totalAmount = price.multiply(new BigDecimal(commodityCount));   //todo 改新字段 俊峰加
            BigDecimal taxAmount = totalAmount.multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
            BigDecimal PriceTaxAmount = totalAmount.add(taxAmount);

            sumDetailInvoiceAmount = sumDetailInvoiceAmount.add(totalAmount);
            sumDetailTaxAmount = sumDetailTaxAmount.add(taxAmount);


            InvoiceDetail invoiceDetail = new InvoiceDetail();
            invoiceDetail.setRowNo(rowNo);
            invoiceDetail.setErpCode(orderDetail.getErpCode());
            invoiceDetail.setErpName(orderDetail.getCommodityName());
            invoiceDetail.setCommodityCount(new BigDecimal(commodityCount));
            invoiceDetail.setPrice(price);
            invoiceDetail.setTotalAmount(totalAmount);
            invoiceDetail.setTaxAmount(taxAmount);
            invoiceDetail.setTaxRate(taxRate);
            invoiceDetail.setPriceTaxAmount(PriceTaxAmount);
            invoiceDetail.setInvoiceLineType(InvoiceLineTypeEnum.REGULAR_LINE.getCode());
            invoiceDetail.setDiscountAmount(BigDecimal.ZERO);
            invoiceDetail.setPolicyStatus("NO");
            invoiceDetail.setPolicyTaxRate(null);
            invoiceDetail.setIsValid(1L);
            invoiceDetail.setCreated(applyTime);
            invoiceDetail.setUpdated(applyTime);
            invoiceDetail.setCreatedBy(createdBy);
            invoiceDetail.setUpdatedBy(createdBy);
            invoiceDetail.setVersion(1L);
            invoiceDetails.add(invoiceDetail);
        }


        Preconditions.checkArgument(invoiceAmount.doubleValue() != sumDetailInvoiceAmount.doubleValue(), "发票头与商品行合计金额不一致");
        BigDecimal priceTaxAmount = sumDetailInvoiceAmount.add(sumDetailTaxAmount);

        invoiceMain.setCompanyCode(command.getInvoiceMain().getCompanyCode());
        invoiceMain.setCompanyName(command.getInvoiceMain().getCompanyName());
        invoiceMain.setOrganizationCode(baseOrderInfo.getOrganizationCode());
        invoiceMain.setOrganizationName(baseOrderInfo.getOrganizationName());
        invoiceMain.setThirdPlatformCode(baseOrderInfo.getThirdPlatformCode());
        invoiceMain.setThirdOrderNo(OfflineThirdOrderNo.thirdOrderNo(baseOrderInfo.getThirdOrderNo()));
        invoiceMain.setOrderNo(OfflineOrderNo.orderNo(String.valueOf(baseOrderInfo.getOrderNo())));
        invoiceMain.setPosNo(baseOrderInfo.getErpSaleNo());
        invoiceMain.setInvoiceStatus(InvoiceStatusEnum.WAIT);
        invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.WAIT);
        invoiceMain.setActualPayAmount(invoiceAmount);
        invoiceMain.setDeliveryAmount(BigDecimal.ZERO);
        invoiceMain.setDeliveryType(deliveryType);
        invoiceMain.setInvoiceAmount(invoiceAmount);
        invoiceMain.setTaxAmount(sumDetailTaxAmount);
        invoiceMain.setPriceTaxAmount(priceTaxAmount);
        invoiceMain.setSplitBill("NOT");
        invoiceMain.setOrderCreated(baseOrderInfo.getCreated());
        invoiceMain.setSellerNumber(baseOrderInfo.getOrganizationCode());
        invoiceMain.setSellerName(baseOrderInfo.getOrganizationName());
        invoiceMain.setIsValid(1L);
        invoiceMain.setCreated(new Date());
        invoiceMain.setUpdated(new Date());
        invoiceMain.setVersion(1L);

        command.setInvoiceMain(invoiceMain);
        command.setDetails(invoiceDetails);


        return command;
    }


    public static ApplyInvoiceCommand applyInvoiceConvertByOnlineOrderB2C(B2cOrderAllDetailRes data, List<B2cRefundAllDetailRes> refundDataList, ApplyInvoiceCommand command, List<AveragePriceVO> detailPriceList) {
        B2cOmsOrderInfo baseOrderInfo = data.getOmsOrderInfo();
        InvoiceMain invoiceMain = command.getInvoiceMain();
        Date applyTime = invoiceMain.getApplyTime();
        String createdBy = invoiceMain.getCreatedBy();
        String deliveryType = InvoiceDeliveryTypeEnum.MERCHANT_FULFILLMENT.getCode();

        BigDecimal sumDetailInvoiceAmount = BigDecimal.ZERO;
        BigDecimal sumDetailTaxAmount = BigDecimal.ZERO;

        List<InvoiceAmount> invoiceAmounts = InvoiceAmountConvert.convertByOnlineOrderB2C(data, refundDataList);
        Optional<InvoiceAmount> first = invoiceAmounts.stream().filter(invoiceAmount -> invoiceAmount.getKey().equals(command.getInvoiceAmount().getKey())).findFirst();
        Preconditions.checkArgument(first.isPresent(), "请选择发票金额开具方式");
        BigDecimal invoiceAmount = first.get().getAmount();

        Map<String, AveragePriceVO> priceVOMap = detailPriceList.stream().collect(Collectors.toMap(AveragePriceVO::getErpCode, d -> d));
        Map<String, List<B2cRefundDetail>> mapByRefundDetailList = refundDataList.stream().flatMap(model -> model.getRefundDetailList().stream()).collect(Collectors.groupingBy(B2cRefundDetail::getThirdDetailId));


        List<InvoiceDetail> invoiceDetails = new ArrayList<>();
        for (B2cOrderDetail orderDetail : data.getOrderDetailList()) {
            if (orderDetail.getStatus() != 0) {
                continue;
            }
            Integer commodityCount = orderDetail.getCommodityCount();
            String rowNo = orderDetail.getThirdDetailId();
            List<B2cRefundDetail> refundDetails = mapByRefundDetailList.get(rowNo);
            if (!refundDetails.isEmpty()) {
                for (B2cRefundDetail refundDetail : refundDetails) {
                    commodityCount = commodityCount - (refundDetail.getRefundCount());
                }
            }
            if (commodityCount.intValue() <= 0) {
                continue;
            }
            AveragePriceVO averagePriceVO = priceVOMap.get(orderDetail.getErpCode());
            BigDecimal taxRate = new BigDecimal(averagePriceVO.getTaxRate());
            BigDecimal price = orderDetail.getPrice();
            BigDecimal totalAmount = price.multiply(new BigDecimal(commodityCount));  //todo 改新字段 俊峰加
            BigDecimal taxAmount = totalAmount.multiply(taxRate).setScale(2, RoundingMode.HALF_UP);
            BigDecimal PriceTaxAmount = totalAmount.add(taxAmount);

            sumDetailInvoiceAmount = sumDetailInvoiceAmount.add(totalAmount);
            sumDetailTaxAmount = sumDetailTaxAmount.add(taxAmount);


            InvoiceDetail invoiceDetail = new InvoiceDetail();
            invoiceDetail.setRowNo(rowNo);
            invoiceDetail.setErpCode(orderDetail.getErpCode());
            invoiceDetail.setErpName(orderDetail.getCommodityName());
            invoiceDetail.setCommodityCount(new BigDecimal(commodityCount));
            invoiceDetail.setPrice(price);
            invoiceDetail.setTotalAmount(totalAmount);
            invoiceDetail.setTaxAmount(taxAmount);
            invoiceDetail.setTaxRate(taxRate);
            invoiceDetail.setPriceTaxAmount(PriceTaxAmount);
            invoiceDetail.setInvoiceLineType(InvoiceLineTypeEnum.REGULAR_LINE.getCode());
            invoiceDetail.setDiscountAmount(BigDecimal.ZERO);
            invoiceDetail.setPolicyStatus("NO");
            invoiceDetail.setPolicyTaxRate(null);
            invoiceDetail.setIsValid(1L);
            invoiceDetail.setCreated(applyTime);
            invoiceDetail.setUpdated(applyTime);
            invoiceDetail.setCreatedBy(createdBy);
            invoiceDetail.setUpdatedBy(createdBy);
            invoiceDetail.setVersion(1L);
            invoiceDetails.add(invoiceDetail);
        }


        Preconditions.checkArgument(invoiceAmount.doubleValue() != sumDetailInvoiceAmount.doubleValue(), "发票头与商品行合计金额不一致");
        BigDecimal priceTaxAmount = sumDetailInvoiceAmount.add(sumDetailTaxAmount);

        invoiceMain.setCompanyCode(command.getInvoiceMain().getCompanyCode());
        invoiceMain.setCompanyName(command.getInvoiceMain().getCompanyName());
        invoiceMain.setOrganizationCode(baseOrderInfo.getOrganizationCode());
        invoiceMain.setOrganizationName(baseOrderInfo.getOrganizationName());
        invoiceMain.setThirdPlatformCode(baseOrderInfo.getThirdPlatformCode());
        invoiceMain.setThirdOrderNo(OfflineThirdOrderNo.thirdOrderNo(baseOrderInfo.getThirdOrderNo()));
        invoiceMain.setOrderNo(OfflineOrderNo.orderNo(String.valueOf(baseOrderInfo.getOrderNo())));
        invoiceMain.setPosNo(baseOrderInfo.getErpSaleNo());
        invoiceMain.setInvoiceStatus(InvoiceStatusEnum.WAIT);
        invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.WAIT);
        invoiceMain.setActualPayAmount(invoiceAmount);
        invoiceMain.setDeliveryAmount(BigDecimal.ZERO);
        invoiceMain.setDeliveryType(deliveryType);
        invoiceMain.setInvoiceAmount(invoiceAmount);
        invoiceMain.setTaxAmount(sumDetailTaxAmount);
        invoiceMain.setPriceTaxAmount(priceTaxAmount);
        invoiceMain.setSplitBill("NOT");
        invoiceMain.setOrderCreated(OrderDateUtil.LocalDateTimeToDate(baseOrderInfo.getCreated()));
        invoiceMain.setSellerNumber(baseOrderInfo.getOrganizationCode());
        invoiceMain.setSellerName(baseOrderInfo.getOrganizationName());
        invoiceMain.setIsValid(1L);
        invoiceMain.setCreated(new Date());
        invoiceMain.setUpdated(new Date());
        invoiceMain.setVersion(1L);

        command.setInvoiceMain(invoiceMain);
        command.setDetails(invoiceDetails);


        return command;


    }
}
