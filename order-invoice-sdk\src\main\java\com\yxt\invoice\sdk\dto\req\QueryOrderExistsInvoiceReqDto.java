package com.yxt.invoice.sdk.dto.req;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class QueryOrderExistsInvoiceReqDto  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", required = true, example = "ORDER20250811001")
    private String orderNo;


    /**
     * 交易场景 ONLINE:线上交易, OFFLINE:线下交易
     */
    @ApiModelProperty(value = "交易场景 ONLINE:线上交易, OFFLINE:线下交易", required = true, allowableValues = "ONLINE,OFFLINE")
    private String transactionChannel;

    /**
     * 业务类型 O2O, B2C
     */
    @ApiModelProperty(value = "业务类型", required = true, example = "O2O", allowableValues = "O2O,B2C")
    private String businessType;

    /**
     * posNo
     */
    @ApiModelProperty(value = "POS单号", notes = "线下交易时必填", example = "POS20250811001")
    private String posNo;



}
