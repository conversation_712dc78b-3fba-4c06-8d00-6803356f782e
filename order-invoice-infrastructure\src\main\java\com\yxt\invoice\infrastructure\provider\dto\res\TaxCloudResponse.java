package com.yxt.invoice.infrastructure.provider.dto.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TaxCloudResponse<T>  {

    /**
     * 0 成功，1 失败
     */
    private String code;

    /**
     * 消息
     */
    private String msg;

    private String receiveData;

    /**
     * 蓝票受理业务流水号
     */
    private String responseId;


    @ApiModelProperty("请求返回实体对象")
    private T data;


    public boolean success() {
        return "0".equals(code);
    }
}
