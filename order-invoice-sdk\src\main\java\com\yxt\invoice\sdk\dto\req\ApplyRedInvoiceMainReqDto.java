package com.yxt.invoice.sdk.dto.req;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ApplyRedInvoiceMainReqDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商户编码
     */
    @ApiModelProperty("商户编码,必填")
    private String merCode;

    /**
     * 订单号
     */
    @ApiModelProperty("订单号,必填")
    private String orderNo;


    /**
     * 业务类型 O2O, B2C
     */
    @ApiModelProperty("业务类型 O2O、B2C, 线下单填O2O , 必填 ")
    private String businessType;


    /**
     * 交易场景 ONLINE:线上交易, OFFLINE:线下交易
     */
    @ApiModelProperty("交易场景 ONLINE:线上交易, OFFLINE:线下交易,必填")
    private String transactionChannel;




    /**
     * 红冲对应原发票号,红票必填
     */
    @ApiModelProperty("红冲对应原发票号,红票必填")
    private String redInvoiceMainNo;

    /**
     * 红冲原因
     * 01: 开票有误
     * 02: 销货退回
     * 03: 服务中止
     * 04: 销售折让
     */
    @ApiModelProperty("红冲原因,01: 开票有误 02: 销货退回 03: 服务中止 04: 销售折让,红票必填")
    private String redInvoiceReason;

    /**
     * 备注
     */
    @ApiModelProperty("备注,可选")
    private String notes;



    /**
     * 申请开票时间
     */
    @ApiModelProperty("申请开票时间,必填")
    private Date applyTime;



    /**
     * 操作人
     */
    @ApiModelProperty("操作人,必填")
    private String operatorUserId;






}
