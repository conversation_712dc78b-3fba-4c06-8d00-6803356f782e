package com.yxt.invoice.interfaces.controller;

import cn.hutool.core.bean.BeanUtil;
import com.yxt.invoice.domain.command.QueryInvoiceDetailCommand;
import com.yxt.invoice.domain.command.QueryInvoiceListCommand;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.interfaces.converter.InvoiceDTOConverter;
import com.yxt.invoice.interfaces.converter.InvoiceRequestConverter;
import com.yxt.invoice.interfaces.service.InvoiceService;
import com.yxt.invoice.sdk.api.InvoiceManageApi;
import com.yxt.invoice.sdk.dto.InvoiceMainDTO;
import com.yxt.invoice.sdk.dto.req.InvoiceDetailReqDto;
import com.yxt.invoice.sdk.dto.req.InvoiceListReqDto;
import com.yxt.invoice.sdk.dto.res.InvoiceDetailResponse;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.permission.YxtOrderPermission;
import com.yxt.permission.YxtOrderPermission.ValidType;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发票控制器 实现SDK接口，提供RESTful API
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@ApiOperation(value = "管理后台接口")
@RestController
@RequestMapping("/api/1.0/invoice/manage")
@Slf4j
public class InvoiceManageController implements InvoiceManageApi {

  @Resource
  private InvoiceService invoiceService;

  /**
   * 查询发票列表-管理后台
   */

  @Override
  @YxtOrderPermission({
      @YxtOrderPermission.PermissionCheck(validType = ValidType.COMPANY_CODE, field = "#req.companyCodeList"),
      @YxtOrderPermission.PermissionCheck(validType = ValidType.STORE_CODE, field = "#req.organizationCodeList")})
  public ResponseBase<PageDTO<InvoiceMainDTO>> invoiceList(InvoiceListReqDto req) {
    try {
      // 转换为Query
      QueryInvoiceListCommand query = BeanUtil.toBean(req, QueryInvoiceListCommand.class);

      // 调用Service层，返回聚合根列表
      PageDTO<InvoiceMain> pageDTO = invoiceService.queryInvoiceList(query);

      // 转换为DTO列表
      List<InvoiceMainDTO> dtoList = pageDTO.getData().stream()
          .map(InvoiceDTOConverter::convertToInvoiceMainDTO).collect(Collectors.toList());

      // 构建分页响应（这里简化处理，实际应该从分页信息中获取）
      PageDTO<InvoiceMainDTO> pageData = new PageDTO<>(query.getCurrentPage(), query.getPageSize());
      pageData.setTotalCount(pageDTO.getTotalCount());
      pageData.setTotalPage(pageDTO.getTotalPage());
      pageData.setData(dtoList);

      log.info("发票列表查询成功，返回{}条记录", dtoList.size());
      return ResponseBase.success(pageData);

    } catch (Exception e) {
      log.error("发票列表查询系统错误：{}", e.getMessage(), e);
      return ResponseBase.fail("SYSTEM_ERROR", "系统繁忙，请稍后重试");
    }
  }

  /**
   * 查询发票详情
   */

  @Override
  public ResponseBase<InvoiceDetailResponse> detail(InvoiceDetailReqDto req) {
    log.info("收到发票详情查询请求，开票单号：{}", req.getInvoiceMainNo());

    try {
      // 转换为Query
      QueryInvoiceDetailCommand command = InvoiceRequestConverter.convertToInvoiceDetailCommand(
          req);

      // 调用Service层，返回聚合根
      InvoiceAggregate aggregate = invoiceService.queryInvoiceDetail(command);

      // 转换聚合根为响应DTO
      InvoiceDetailResponse result = InvoiceDTOConverter.convertToInvoiceDetailResponse(aggregate);

      log.info("发票详情查询成功，开票单号：{}", result.getInvoiceMain().getInvoiceMainNo());
      return ResponseBase.success(result);

    } catch (Exception e) {
      log.error("发票详情查询系统错误：{}", e.getMessage(), e);
      return ResponseBase.fail("SYSTEM_ERROR", "系统繁忙，请稍后重试");
    }
  }


}
