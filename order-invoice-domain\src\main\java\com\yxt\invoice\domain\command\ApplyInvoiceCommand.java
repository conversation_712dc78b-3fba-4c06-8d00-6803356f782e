package com.yxt.invoice.domain.command;

import com.google.common.base.Preconditions;
import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.order.types.invoice.enums.InvoiceAmountKeyEnum;
import com.yxt.order.types.invoice.enums.InvoiceApplyChannelEnum;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceBuyerPartyTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTransactionChannelEnum;
import com.yxt.order.types.invoice.enums.InvoiceTypeEnum;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 发票申请请求
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class ApplyInvoiceCommand implements Serializable {

    private static final long serialVersionUID = 1L;
    @NotEmpty(message = "发票信息不能为空")
    @Valid
    private InvoiceMain invoiceMain;

    /**
     * 发票明细列表
     */
    @NotEmpty(message = "发票明细不能为空")
    @Valid
    private List<InvoiceDetail> details;

    /**
     * 发票金额开具方式
     */
    @NotEmpty(message = "发票金额开具方式")
    @Valid
    private InvoiceAmount invoiceAmount;



    public void validateApplyInvoice() {

        Preconditions.checkArgument(StringUtils.isNotBlank(invoiceMain.getMerCode()), "商户编码不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(invoiceMain.getOrderNo().getOrderNo()), "订单号不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(invoiceMain.getBusinessType()) && (Arrays.stream(
            InvoiceBusinessTypeEnum.values()).map(InvoiceBusinessTypeEnum::name).collect(
            Collectors.toList())).contains(invoiceMain.getBusinessType()), "业务类型不能为空且需合法");
        Preconditions.checkArgument(StringUtils.isNotBlank(invoiceMain.getTransactionChannel()) && (Arrays.stream(
            InvoiceTransactionChannelEnum.values()).map(InvoiceTransactionChannelEnum::getCode).collect(
            Collectors.toList())).contains(invoiceMain.getTransactionChannel()), "交易场景不能为空且需合法");
        Preconditions.checkArgument(StringUtils.isNotBlank(invoiceMain.getUserId()), "会员编号不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(invoiceMain.getInvoiceType().getCode()) && (Arrays.stream(InvoiceTypeEnum.values()).map(InvoiceTypeEnum::getCode).collect(
            Collectors.toList())).contains(invoiceMain.getInvoiceType().getCode()), "发票类型不能为空且需合法");
        Preconditions.checkArgument(null != invoiceMain.getApplyTime(), "申请开票时间不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(invoiceMain.getApplyChannel()) && (Arrays.stream(InvoiceApplyChannelEnum.values()).map(InvoiceApplyChannelEnum::name).collect(
            Collectors.toList())).contains(invoiceMain.getApplyChannel()), "申请渠道不能为空且需合法");
        Preconditions.checkArgument(StringUtils.isNotBlank(invoiceMain.getBuyerPartyType().getCode()) && (Arrays.stream(
            InvoiceBuyerPartyTypeEnum.values()).map(InvoiceBuyerPartyTypeEnum::getCode).collect(
            Collectors.toList())).contains(invoiceMain.getBuyerPartyType().getCode()), "购方类型不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(invoiceMain.getBuyerName()), "购方名称不能为空");
        Preconditions.checkArgument(!invoiceMain.getBuyerName().contains(" "), "购方名称不能包含空格");
        Preconditions.checkArgument(invoiceMain.getBuyerName().matches("^[\\u4e00-\\u9fa5a-zA-Z]+$"), "购方名称只能包含中文和英文字母，不能包含空格及特殊字符");


        Preconditions.checkArgument(StringUtils.isNotBlank(invoiceMain.getBuyerTin()), "购方税号不能为空");
        Preconditions.checkArgument(!invoiceMain.getBuyerTin().contains(" "), "购方税号不能包含空格");
        Preconditions.checkArgument(invoiceMain.getBuyerTin().equals(invoiceMain.getBuyerTin().toUpperCase()), "购方税号必须为大写");
        if (StringUtils.isNotEmpty(invoiceMain.getBuyerPhone())) {
            Preconditions.checkArgument(invoiceMain.getBuyerPhone().matches("^(0\\d{2,3}-)?\\d{7,8}(-\\d{1,6})?$|^1[3-9]\\d{9}$"), "购方电话格式不正确");
        }

        if (StringUtils.isNotEmpty(invoiceMain.getBuyerBankAccount())) {
            String cleanBankAccount = invoiceMain.getBuyerBankAccount().replaceAll("\\s+", "");
            // 银行卡号长度通常为16-19位数字
            Preconditions.checkArgument(cleanBankAccount.matches("^\\d{16,19}$"), "购方银行账户格式不正确，应为16-19位数字");
        }


        if (StringUtils.isNotEmpty(invoiceMain.getBuyerEmail())) {
            Preconditions.checkArgument(invoiceMain.getBuyerEmail().matches("^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"),
                    "购方邮箱格式不正确");
        }

        if (StringUtils.isNotEmpty(invoiceMain.getBuyerMobile())) {
            Preconditions.checkArgument(invoiceMain.getBuyerMobile().matches("^(0\\d{2,3}-)?\\d{7,8}(-\\d{1,6})?$|^1[3-9]\\d{9}$"), "购方电话格式不正确");
        }

        Preconditions.checkArgument(null != invoiceAmount, "发票开具金额方式不能为空");
        Preconditions.checkArgument(Arrays.stream(InvoiceAmountKeyEnum.values()).collect(Collectors.toList()).contains(invoiceAmount.getKey()), "发票开具金额方式不能为空");

    }


}
