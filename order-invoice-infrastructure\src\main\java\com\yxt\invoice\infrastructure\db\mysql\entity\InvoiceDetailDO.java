package com.yxt.invoice.infrastructure.db.mysql.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import java.math.BigDecimal;

import java.util.Date;
import lombok.Data;

/**
 * 发票明细表DO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
@TableName("invoice_detail")
public class InvoiceDetailDO {

  /**
   * 主键ID
   */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 开票单号
   */
  private String invoiceMainNo;

  /**
   * 开票明细单号
   */
  private String invoiceDetailNo;

  /**
   * 行号
   */
  private String rowNo;

  /**
   * 税收分类编码
   */
  private String taxClassificationCode;

  /**
   * 顶级税收分类编码
   */
  private String topLevelTaxClassificationCode;

  /**
   * ERP商品编码
   */
  private String erpCode;

  /**
   * ERP商品名称
   */
  private String erpName;

  /**
   * 商品数量
   */
  private BigDecimal commodityCount;

  /**
   * 规格型号
   */
  private String commoditySpec;

  /**
   * 单位
   */
  private String unit;

  /**
   * 单价
   */
  private BigDecimal price;

  /**
   * 金额
   */
  private BigDecimal totalAmount;

  /**
   * 税额
   */
  private BigDecimal taxAmount;

  /**
   * 税率
   */
  private BigDecimal taxRate;

  /**
   * 税率编码
   */
  private String taxRateCode;

  /**
   * 价税合计
   */
  private BigDecimal priceTaxAmount;

  /**
   * 发票行性质
   */
  private String invoiceLineType;

  /**
   * 折扣金额
   */
  private BigDecimal discountAmount;

  /**
   * 优惠政策标识
   */
  private String policyStatus;

  /**
   * 优惠政策标签
   */
  private String policyTag;

  /**
   * 优惠政策税率
   */
  private BigDecimal policyTaxRate;

  /**
   * 是否有效
   */
  private Long isValid;

  /**
   * 创建时间
   */
  @TableField(fill = FieldFill.INSERT)
  private Date created;

  /**
   * 更新时间
   */
  @TableField(fill = FieldFill.INSERT_UPDATE)
  private Date updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;

  /**
   * 数据版本
   */
  @Version
  private Long version;
}
