package com.yxt.invoice.sdk.dto;

import com.yxt.order.types.invoice.enums.*;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class InvoiceMainDTO {

    /**
     * 发票ID
     */
    @ApiModelProperty("发票ID")
    private Long id;

    /**
     * 分公司编码
     */
    @ApiModelProperty(value = "分公司编码", example = "YXT001")
    private String companyCode;

    /**
     * 分公司名称
     */
    @ApiModelProperty(value = "分公司名称", example = "一心堂药业集团股份有限公司")
    private String companyName;

    /**
     * 所属机构编码
     */
    @ApiModelProperty(value = "所属机构编码", example = "ORG001")
    private String organizationCode;

    /**
     * 所属机构名称
     */
    @ApiModelProperty(value = "所属机构名称", example = "昆明分公司")
    private String organizationName;

    /**
     * 开票单号
     */
    @ApiModelProperty(value = "开票单号", example = "INV20250811001")
    private String invoiceMainNo;

    /**
     * 平台编码
     */
    @ApiModelProperty(value = "平台编码", example = "YXDJ")
    private String thirdPlatformCode;

    /**
     * 第三方平台订单号
     */
    @ApiModelProperty(value = "第三方平台订单号", example = "THIRD20250811001")
    private String thirdOrderNo;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", example = "ORDER20250811001")
    private String orderNo;

    /**
     * pos销售单号
     */
    @ApiModelProperty(value = "POS销售单号", example = "POS20250811001")
    private String posNo;


    /**
     * 会员编号
     */
    @ApiModelProperty(value = "会员编号", example = "USER123456")
    private String userId;

    /**
     * 商户编码
     */
    @ApiModelProperty(value = "商户编码", example = "MER001")
    private String merCode;

    /**
     * 交易场景 ONLINE:线上交易, OFFLINE:线下交易
     */
    @ApiModelProperty(value = "交易场景 ONLINE:线上交易, OFFLINE:线下交易")
    private String transactionChannel;

    /**
     * 发票供应方编码
     */
    @ApiModelProperty(value = "发票供应方编码", example = "PROVIDER001")
    private String providerCode;

    /**
     * 发票代码（税务云回调写入）
     */
    @ApiModelProperty(value = "发票代码", example = "053001900111")
    private String invoiceCode;

    /**
     * 发票号码（税务云回调写入）
     */
    @ApiModelProperty(value = "发票号码", example = "********")
    private String invoiceNo;

    /**
     * 蓝票:TAX_INVOICE 红票:CREDIT_NOTE
     */
    @ApiModelProperty(value = "发票红蓝类型 蓝票:TAX_INVOICE 红票:CREDIT_NOTE")
    private InvoiceRedBlueTypeEnum invoiceRedBlueType;

    /**
     * 红冲对应原发票号,红票必填
     */
    @ApiModelProperty(value = "红冲对应原发票号", example = "INV20250810001")
    private String redInvoiceMainNo;

    /**
     * 红冲原因
     * 01: 开票有误
     * 02: 销货退回
     * 03: 服务中止
     * 04: 销售折让
     */
    @ApiModelProperty(value = "红冲原因", example = "01", allowableValues = "01,02,03,04")
    private String redInvoiceReason;

    /**
     *  备注
     */
    @ApiModelProperty(value = "备注", example = "药品销售")
    private String notes;

    /**
     * 发票类型
     */
    @ApiModelProperty(value = "发票类型 专票:SPECIAL_INVOICE 普票:ORDINARY_INVOICE")
    private InvoiceTypeEnum invoiceType;

    /**
     * 发票状态
     */
    @ApiModelProperty(value = "发票状态 WAIT-待开票; PROGRESSING-开票中 SUCCESS-开票成功; FAIL-开票失败 ; WAIT_RED-待红冲 ; RED_PROGRESSING-红冲中 RED_SUCCESS-红冲成功 RED_FAIL-红冲失败")
    private InvoiceStatusEnum invoiceStatus;

    /**
     * 同步状态
     */
    @ApiModelProperty(value = "同步状态 WAIT-待处理; DONE-已回传平台")
    private InvoiceSyncStatusEnum syncStatus;

    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实付金额", example = "158.50")
    private BigDecimal actualPayAmount;

    /**
     * 配送费
     */
    @ApiModelProperty(value = "配送费", example = "8.50")
    private BigDecimal deliveryAmount;

    /**
     * 配送方式 PLATFORM_FULFILLMENT:平台配送 MERCHANT_FULFILLMENT:商家自配
     */
    @ApiModelProperty(value = "配送方式 PLATFORM_FULFILLMENT:平台配送 MERCHANT_FULFILLMENT:商家自配")
    private String deliveryType;

    /**
     * 发票金额
     */
    @ApiModelProperty(value = "发票金额", example = "150.00")
    private BigDecimal invoiceAmount;

    /**
     * 税额
     */
    @ApiModelProperty(value = "税额", example = "19.50")
    private BigDecimal taxAmount;

    /**
     * 价税合计
     */
    @ApiModelProperty(value = "价税合计", example = "169.50")
    private BigDecimal priceTaxAmount;

    /**
     * 拆票标记 SPLIT-拆 NOT-不拆
     */
    @ApiModelProperty(value = "拆票标记", example = "NOT", allowableValues = "SPLIT,NOT")
    private String splitBill;

    /**
     * 订单创单时间
     */
    @ApiModelProperty(value = "订单创单时间", example = "2025-08-11 10:30:00")
    private Date orderCreated;

    /**
     * 电子发票PDF地址
     */
    @ApiModelProperty(value = "电子发票PDF地址", example = "https://invoice.yxt.com/pdf/INV20250811001.pdf")
    private String pdfUrl;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因", example = "税号格式错误")
    private String invoiceErrMsg;

    /**
     * 申请开票时间
     */
    @ApiModelProperty(value = "申请开票时间", example = "2025-08-11 14:30:00")
    private Date applyTime;

    /**
     * 开票人
     */
    @ApiModelProperty(value = "开票人", example = "张三")
    private String operator;

    /**
     * 收款人
     */
    @ApiModelProperty(value = "收款人", example = "李四")
    private String payee;

    /**
     * 复核人
     */
    @ApiModelProperty(value = "复核人", example = "王五")
    private String reviewed;

    /**
     * 申请渠道 一心到家-YXDJ 心云-XY 海典H2-H2POS
     */
    @ApiModelProperty(value = "申请渠道", example = "YXDJ", allowableValues = "YXDJ,XY,H2POS")
    private String applyChannel;

    /**
     * 开票主体
     */
    @ApiModelProperty(value = "开票主体", example = "SELLER001")
    private String sellerNumber;

    /**
     * 开票主体名称
     */
    @ApiModelProperty(value = "开票主体名称", example = "一心堂药业集团股份有限公司")
    private String sellerName;

    /**
     * 开票纳税人识别号
     */
    @ApiModelProperty(value = "开票纳税人识别号", example = "915300007********9")
    private String sellerTin;

    /**
     * 开票主体地址
     */
    @ApiModelProperty(value = "开票主体地址", example = "云南省昆明市盘龙区北京路XXX号")
    private String sellerAddress;

    /**
     * 开票主体电话
     */
    @ApiModelProperty(value = "开票主体电话", example = "0871-********")
    private String sellerPhone;

    /**
     * 开票主体银行
     */
    @ApiModelProperty(value = "开票主体银行", example = "中国工商银行昆明分行")
    private String sellerBank;

    /**
     * 开票主体银行账户
     */
    @ApiModelProperty(value = "开票主体银行账户", example = "********90********9")
    private String sellerBankAccount;

    /**
     * 购方类型
     */
    @ApiModelProperty(value = "购方类型 个人-INDIVIDUAL 单位-ORGANIZATION")
    private InvoiceBuyerPartyTypeEnum buyerPartyType;

    /**
     * 购方名称
     */
    @ApiModelProperty(value = "购方名称", example = "张三")
    private String buyerName;

    /**
     * 购方税号（个人身份证/单位纳税人识别号）
     */
    @ApiModelProperty(value = "购方税号", example = "530102199001011234")
    private String buyerTin;

    /**
     * 购方地址
     */
    @ApiModelProperty(value = "购方地址", example = "云南省昆明市五华区XXX路XXX号")
    private String buyerAddress;

    /**
     * 购方电话
     */
    @ApiModelProperty(value = "购方电话", example = "***********")
    private String buyerPhone;

    /**
     * 购方银行
     */
    @ApiModelProperty(value = "购方银行", example = "中国建设银行昆明分行")
    private String buyerBank;

    /**
     * 购方银行账户
     */
    @ApiModelProperty(value = "购方银行账户", example = "9876543210987654321")
    private String buyerBankAccount;

    /**
     * 购方邮箱
     */
    @ApiModelProperty(value = "购方邮箱", example = "<EMAIL>")
    private String buyerEmail;

    /**
     * 购方手机号
     */
    @ApiModelProperty(value = "购方手机号", example = "***********")
    private String buyerMobile;

    /**
     * 显示购方银行账户 SHOW-显示 HIDE-不显示
     */
    @ApiModelProperty(value = "显示购方银行账户", example = "HIDE", allowableValues = "SHOW,HIDE")
    private String showBuyerBankAccount;

    /**
     * 是否起效 1-起效 -1-未起效
     */
    @ApiModelProperty(value = "是否起效 1-起效 -1-未起效")
    private Long isValid;

    /**
     * 平台创建时间
     */
    @ApiModelProperty(value = "平台创建时间", example = "2025-08-11 14:30:00")
    private Date created;

    /**
     * 平台更新时间
     */
    @ApiModelProperty(value = "平台更新时间", example = "2025-08-11 14:35:00")
    private Date updated;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", example = "system")
    private String createdBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", example = "system")
    private String updatedBy;

    /**
     * 系统创建时间
     */
    @ApiModelProperty(value = "系统创建时间", example = "2025-08-11 14:30:00")
    private Date sysCreateTime;

    /**
     * 系统更新时间
     */
    @ApiModelProperty(value = "系统更新时间", example = "2025-08-11 14:35:00")
    private Date sysUpdateTime;

    /**
     * 数据版本，每次update+1
     */
    @ApiModelProperty(value = "数据版本", example = "1")
    private Long version;


}
