package com.yxt.invoice.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.yxt.invoice.domain.command.ExistsOrderInvoiceCommand;
import com.yxt.invoice.domain.command.QueryInvoiceListCommand;
import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.repository.InvoiceRepository;
import com.yxt.invoice.domain.utils.OrderDateUtils;
import com.yxt.invoice.infrastructure.converter.InvoiceDOConverter;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.doc.EsInvoiceMain;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.mapper.EsInvoiceMainMapper;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceDetailDO;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceMainDO;
import com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceDetailMapper;
import com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceMainMapper;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryData;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryReq;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosResponse;
import com.yxt.invoice.infrastructure.provider.feign.HdPosFeign;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 发票仓储实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Repository
@Slf4j
public class InvoiceRepositoryImpl implements InvoiceRepository {


  @Autowired
  private InvoiceMainMapper invoiceMainMapper;


  @Autowired
  private InvoiceDetailMapper invoiceDetailMapper;


  @Resource
  private HdPosFeign hdPosFeign;


  @Resource
  private EsInvoiceMainMapper esInvoiceMainMapper;






  @Override
  public PageDTO<InvoiceMain> findInvoiceManByConditions(QueryInvoiceListCommand query) {
    LambdaEsQueryWrapper<EsInvoiceMain> queryWrapper = new LambdaEsQueryWrapper<>();

    queryWrapper.in(!CollectionUtils.isEmpty(query.getCompanyCodeList()),
        EsInvoiceMain::getCompanyCode, query.getCompanyCodeList());
    queryWrapper.in(!CollectionUtils.isEmpty(query.getOrganizationCodeList()),
        EsInvoiceMain::getOrganizationCode, query.getOrganizationCodeList());
    queryWrapper.eq(StringUtils.isNotEmpty(query.getThirdOrderNo()),
        EsInvoiceMain::getThirdOrderNo, query.getThirdOrderNo());
    queryWrapper.eq(StringUtils.isNotEmpty(query.getPosNo()), EsInvoiceMain::getPosNo,
        query.getPosNo());
    queryWrapper.ge(Objects.nonNull(query.getApplyTimeStart()), EsInvoiceMain::getApplyTime,
        OrderDateUtils.formatYYMMDD(query.getApplyTimeStart()));
    queryWrapper.le(Objects.nonNull(query.getApplyTimeEnd()), EsInvoiceMain::getApplyTime,
        OrderDateUtils.formatYYMMDD(query.getApplyTimeEnd()));
    queryWrapper.eq(StringUtils.isNotEmpty(query.getInvoiceRedBlueType()),
        EsInvoiceMain::getInvoiceRedBlueType, query.getInvoiceRedBlueType());

    queryWrapper.eq(StringUtils.isNotEmpty(query.getInvoiceStatus()),
        EsInvoiceMain::getInvoiceStatus, query.getInvoiceStatus());
    queryWrapper.eq(StringUtils.isNotEmpty(query.getSyncStatus()), EsInvoiceMain::getSyncStatus,
        query.getSyncStatus());
    queryWrapper.eq(EsInvoiceMain::getIsValid, 1);

    queryWrapper.orderByDesc(EsInvoiceMain::getApplyTime);
    EsPageInfo<EsInvoiceMain> esPage = esInvoiceMainMapper.pageQuery(queryWrapper,
        query.getCurrentPage().intValue(), query.getPageSize().intValue());

    List<InvoiceMain> resList = Lists.newArrayList();
    if (esPage.getTotal() != 0L) {
      resList = esPage.getList().stream().map(esInvoiceMain -> {
        LambdaQueryWrapper<InvoiceMainDO> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(InvoiceMainDO::getInvoiceMainNo, esInvoiceMain.getInvoiceMainNo());
        InvoiceMainDO invoiceMainDO = invoiceMainMapper.selectOne(detailWrapper);
        return InvoiceDOConverter.toDomain(invoiceMainDO);
      }).collect(Collectors.toList());
    }
    PageDTO<InvoiceMain> pageRes = new PageDTO<>();
    pageRes.setTotalCount(esPage.getTotal());
    pageRes.setTotalPage((long) esPage.getPages());
    pageRes.setData(resList);
    pageRes.setCurrentPage(query.getCurrentPage());
    pageRes.setPageSize(query.getPageSize());
    return pageRes;
  }

  /**
   * 步骤:
   * 1. 先查本地
   * 2. 再查远程
   * @param command
   * @return
   */
  @Override
  public Boolean checkExistsFromDbAndRemote(ExistsOrderInvoiceCommand command) {
    LambdaQueryWrapper<InvoiceMainDO> detailWrapper = new LambdaQueryWrapper<>();
    detailWrapper.eq(InvoiceMainDO::getOrderNo, command.getOrderNo())
        .eq(InvoiceMainDO::getIsValid, 1)
        .eq(InvoiceMainDO::getInvoiceRedBlueType, InvoiceRedBlueTypeEnum.TAX_INVOICE.getCode());

    List<InvoiceMainDO> invoiceMainDOList = invoiceMainMapper.selectList(detailWrapper);
    if (!invoiceMainDOList.isEmpty()) {
      return true;
    }

    PosInvoiceQueryReq req = new PosInvoiceQueryReq();
    req.setThirdOrderNo(command.getThirdOrderNo().getThirdOrderNo());
    req.setStoreCode(command.getStoreCode());

    PosResponse<List<PosInvoiceQueryData>> posResponse = hdPosFeign.invoiceQuery(req);
    Boolean success = posResponse.success();
    if (success) {
      List<PosInvoiceQueryData> dataList = posResponse.getData();
      if (CollectionUtils.isEmpty(dataList)) {
        return Boolean.FALSE;
      }

      // 主要看responseId有值的数据
      List<PosInvoiceQueryData> validDataList = dataList.stream()
          .filter(s -> StringUtils.isNotEmpty(s.getResponseId())).collect(Collectors.toList());
      if (CollectionUtils.isEmpty(validDataList)) {
        return Boolean.FALSE;
      } else {
        return Boolean.TRUE;
      }
    }
    return Boolean.FALSE;
  }

  @Override
  @Transactional
  public InvoiceAggregate doSave(InvoiceAggregate aggregate) {
    InvoiceMain invoice = aggregate.getInvoiceMain();
    List<InvoiceDetail> invoiceDetailList = aggregate.getInvoiceDetailList();
    InvoiceMainDO invoiceMainDO = InvoiceDOConverter.toDO(invoice);
    List<InvoiceDetailDO> invoiceDetailDOList = invoiceDetailList.stream()
        .map(InvoiceDOConverter::toDO).collect(Collectors.toList());
    int insertInvoice = invoiceMainMapper.insert(invoiceMainDO);
    if (insertInvoice <= 0) {
      throw new RuntimeException("插入发票主表失败");
    }

    if (!CollectionUtils.isEmpty(invoiceDetailDOList)) {
      for (InvoiceDetailDO invoiceDetailDO : invoiceDetailDOList) {
        int insertDetail = invoiceDetailMapper.insert(invoiceDetailDO);
        if (insertDetail <= 0) {
          throw new RuntimeException("插入发票明细失败");
        }
      }
    }
    return aggregate;
  }

  @Override
  @Transactional
  public InvoiceAggregate doSaveRedInvoice(InvoiceAggregate invoiceAggregate,
      InvoiceAggregate redInvoiceAggregate) {

    Preconditions.checkArgument(
        !invoiceAggregate.isRedCreditInvoice() && redInvoiceAggregate.isRedCreditInvoice(),
        "冲红遇到非法数据,请检查");
    doSave(redInvoiceAggregate);


    //更新原始发票状态
    InvoiceMainDO originInvoice = new InvoiceMainDO();
    originInvoice.setInvoiceStatus(InvoiceStatusEnum.RED_PROGRESSING.getCode());
    originInvoice.setUpdated(new Date());
    originInvoice.setVersion(invoiceAggregate.getInvoiceMain().getVersion() + 1);
    int insert2 = invoiceMainMapper.update(originInvoice,
        new LambdaQueryWrapper<InvoiceMainDO>().eq(InvoiceMainDO::getInvoiceMainNo,
                invoiceAggregate.getInvoiceMain().getRedInvoiceMainNo())
            .lt(InvoiceMainDO::getVersion, originInvoice.getVersion()));
    if (insert2 <= 0) {
      throw new RuntimeException("更新原始发票失败");
    }
    return redInvoiceAggregate;
  }

  @Override
  public List<InvoiceAggregate> findByOrderNo(String orderNo) {

    // 使用LambdaQueryWrapper查询主表
    LambdaQueryWrapper<InvoiceMainDO> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(InvoiceMainDO::getOrderNo, orderNo)
        .eq(InvoiceMainDO::getInvoiceRedBlueType, InvoiceRedBlueTypeEnum.TAX_INVOICE.getCode())
        .eq(InvoiceMainDO::getIsValid, 1);

    List<InvoiceMainDO> invoiceMainDOList = invoiceMainMapper.selectList(wrapper);
    if (invoiceMainDOList.isEmpty()) {
      return null;
    }
    List<InvoiceAggregate> invoiceAggregateList = new ArrayList<>();
    for (InvoiceMainDO invoiceMainDO : invoiceMainDOList) {

      // 使用LambdaQueryWrapper查询明细
      LambdaQueryWrapper<InvoiceDetailDO> detailWrapper = new LambdaQueryWrapper<>();
      detailWrapper.eq(InvoiceDetailDO::getInvoiceMainNo, invoiceMainDO.getInvoiceMainNo())
              .eq(InvoiceDetailDO::getIsValid, 1).orderByAsc(InvoiceDetailDO::getRowNo);

      List<InvoiceDetailDO> detailDOList = invoiceDetailMapper.selectList(detailWrapper);

      // 转换为Domain对象并重建聚合根
      InvoiceMain invoice = InvoiceDOConverter.toDomain(invoiceMainDO);
      List<InvoiceDetail> details = InvoiceDOConverter.toDetailDomainList(detailDOList);
      invoiceAggregateList.add(InvoiceAggregate.create(invoice, details));
    }



    return invoiceAggregateList;
  }

  @Override
  public InvoiceAggregate findByInvoiceMainNo(String invoiceMainNo) {
    log.info("根据开票单号查询发票，开票单号：{}", invoiceMainNo);

    // 使用LambdaQueryWrapper查询主表
    LambdaQueryWrapper<InvoiceMainDO> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(InvoiceMainDO::getInvoiceMainNo, invoiceMainNo);

    InvoiceMainDO invoiceMainDO = invoiceMainMapper.selectOne(wrapper);
    if (invoiceMainDO == null) {
      return null;
    }

    // 使用LambdaQueryWrapper查询明细
    LambdaQueryWrapper<InvoiceDetailDO> detailWrapper = new LambdaQueryWrapper<>();
    detailWrapper.eq(InvoiceDetailDO::getInvoiceMainNo, invoiceMainNo)
        .orderByAsc(InvoiceDetailDO::getRowNo);

    List<InvoiceDetailDO> detailDOList = invoiceDetailMapper.selectList(detailWrapper);

    // 转换为Domain对象并重建聚合根
    InvoiceMain invoice = InvoiceDOConverter.toDomain(invoiceMainDO);
    List<InvoiceDetail> details = InvoiceDOConverter.toDetailDomainList(detailDOList);

    return InvoiceAggregate.create(invoice, details);
  }

}
