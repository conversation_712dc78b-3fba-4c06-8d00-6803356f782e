package com.yxt.invoice.infrastructure.event;

import static com.yxt.order.common.utils.OrderDateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS;


import com.yxt.invoice.domain.event.create.PullBackInvoiceInfoEvent;
import com.yxt.invoice.domain.event.create.PullBackInvoiceInfoEvent.Data;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.repository.InvoiceRepository;
import com.yxt.invoice.infrastructure.common.configration.RemoteProperties;
import com.yxt.invoice.infrastructure.common.configration.RemoteProperties.TaxCloud;
import com.yxt.invoice.infrastructure.common.utils.LogUtils;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryData;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryReq;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosResponse;
import com.yxt.invoice.infrastructure.provider.dto.req.GetInvoiceByResponseIdReqDto;
import com.yxt.invoice.infrastructure.provider.dto.res.GetInvoiceByResponseIdResDto;
import com.yxt.invoice.infrastructure.provider.dto.res.TaxCloudResponse;
import com.yxt.invoice.infrastructure.provider.feign.HdPosFeign;
import com.yxt.invoice.infrastructure.provider.feign.TaxCloudFeign;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.common.utils.OrderDateUtil;
import com.yxt.order.types.invoice.enums.InvoiceIsValidEnum;
import com.yxt.order.types.invoice.enums.InvoiceLogTypeApiEnum;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import com.yxt.order.types.invoice.enums.InvoiceSyncStatusEnum;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@Slf4j
public class InvoiceCheckProviderPullEventHandler implements EventHandler {

  @Resource
  private InvoiceRepository invoiceRepository;

  @Resource
  private HdPosFeign hdPosFeign;

  @Resource
  private TaxCloudFeign taxCloudFeign;

  @Resource
  private RemoteProperties remoteProperties;

  @Override
  public void handle(PullBackInvoiceInfoEvent event) {

    Data data = event.getData();
    String storeCode = data.getStoreCode();
    String thirdOrderNo = data.getThirdOrderNo();

    PosInvoiceQueryReq req = new PosInvoiceQueryReq();
    req.setStoreCode(storeCode);
    req.setThirdOrderNo(thirdOrderNo);

    PosResponse<List<PosInvoiceQueryData>> posResponse = hdPosFeign.invoiceQuery(req);
    if (!posResponse.success()) {
      log.warn("hdPosFeign.invoiceQuery 接口调用失败请求参数:{} 响应参数:{}", JsonUtils.toJson(req),
          JsonUtils.toJson(posResponse));
      return;
    }

    // 获取到responseId
    List<PosInvoiceQueryData> dataList = posResponse.getData();
    if (CollectionUtils.isEmpty(dataList)) {
      return;
    }
    List<PosInvoiceQueryData> validDataList = dataList.stream()
        .filter(s -> StringUtils.isNotEmpty(s.getResponseId())).collect(Collectors.toList());

    for (PosInvoiceQueryData posInvoiceQueryData : validDataList) {
      saveInvoiceMain(posInvoiceQueryData, data);
    }
  }


  private void saveInvoiceMain(PosInvoiceQueryData posInvoiceQueryData, Data data) {
    InvoiceAggregate invoiceAggregate = data.getInvoiceAggregate();
    InvoiceMain invoiceMain = invoiceAggregate.getInvoiceMain();
    String invoiceMainNo = invoiceMain.getInvoiceMainNo();

    String responseId = posInvoiceQueryData.getResponseId();

    TaxCloud taxCloud = remoteProperties.getTaxCloud();

    GetInvoiceByResponseIdReqDto reqDto = new GetInvoiceByResponseIdReqDto();
    reqDto.setResponseId(responseId);
    reqDto.setOutRequestCode(Strings.EMPTY);
    reqDto.setInvoiceTag(Strings.EMPTY);
    reqDto.setPId(taxCloud.getPId());
    reqDto.setPSecret(taxCloud.getPSecret());
    reqDto.setPId(remoteProperties.getTaxCloud().getPId());
    reqDto.setPSecret(remoteProperties.getTaxCloud().getPSecret());

    LogUtils.logApi(invoiceMainNo,String.format("请求内容: %s", JsonUtils.toJson(reqDto)),
        InvoiceLogTypeApiEnum.API_REQUEST.name(), getPosition());
    TaxCloudResponse<GetInvoiceByResponseIdResDto> taxCloudResponse = taxCloudFeign.getInvoiceByOutRequestCode(
        reqDto);
    LogUtils.logApi(invoiceMainNo,String.format("响应内容: %s", JsonUtils.toJson(taxCloudResponse)),
        InvoiceLogTypeApiEnum.API_RESPONSE.name(),getPosition());

    if (!taxCloudResponse.success()) {
      log.warn("taxCloudFeign.getInvoiceByOutRequestCode 接口调用失败请求参数:{} 响应参数:{}",
          JsonUtils.toJson(reqDto), JsonUtils.toJson(taxCloudResponse));
      return;
    }

    GetInvoiceByResponseIdResDto taxCloudData = taxCloudResponse.getData();


    invoiceMain.cleanSellerAndBuyerInfo();
    invoiceMain.setInvoiceCode(taxCloudData.getInvoiceCode());
    invoiceMain.setInvoiceNo(taxCloudData.getResponseId()); // 业务流水号

    InvoiceRedBlueTypeEnum invoiceRedBlueTypeEnum = null;
    // invoiceTag 发票标识 0 蓝票，1 红票
    String invoiceTag = taxCloudData.getInvoiceTag();
    if ("0".equals(invoiceTag)) {
      invoiceRedBlueTypeEnum = InvoiceRedBlueTypeEnum.TAX_INVOICE;
    } else if ("1".equals(invoiceTag)) {
      invoiceRedBlueTypeEnum = InvoiceRedBlueTypeEnum.CREDIT_NOTE;
    } else {
      throw new RuntimeException(String.format("系统暂不支持: invoiceTag: %s", invoiceTag));
    }
    invoiceMain.setInvoiceRedBlueType(invoiceRedBlueTypeEnum);
    String invoiceCloudStatus = taxCloudData.getInvoiceStatus();
    invoiceMain.setInvoiceStatus(mappingToInvoiceStatus(invoiceCloudStatus));
    invoiceMain.setSyncStatus(InvoiceSyncStatusEnum.DONE); // 从平台查出来的,默认已经成功
    invoiceMain.setPdfUrl(taxCloudData.getInvoicePdf());
    invoiceMain.setIsValid(InvoiceIsValidEnum.VALID.getCode());
    Date issueDate = OrderDateUtil.parseStrToDate(taxCloudData.getIssueDate(),DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
    invoiceMain.setApplyTime(issueDate); // 使用完成时间当做申请开票时间
    invoiceMain.setCompleteTime(issueDate);
    invoiceMain.setCreated(issueDate);
    invoiceMain.setUpdated(issueDate);
    invoiceMain.setSysCreateTime(new Date());
    invoiceMain.setSysUpdateTime(new Date());

//    if ("1".equals(invoiceTag)) { // 是红票  notey: 感觉不用处理。这个是红冲过程记录不用处理。最终结果还是上面的invoiceStatus
//      invoiceMain.setInvoiceStatus(reMappingRedStatus(taxCloudData.getWriteOffState()));
//    }
//    invoiceMain.setRedInvoiceReason(taxCloudData.getStatusMsg());

    // 开票主体还是购方信息不用处理

    InvoiceAggregate rebuild = InvoiceAggregate.create(invoiceMain, null);
    invoiceRepository.doSave(rebuild);
  }

  @NotNull
  private String getPosition() {
    return this.getClass().getName() + ":tId:" + Thread.currentThread().getId();
  }

  private InvoiceStatusEnum reMappingRedStatus(String writeOffState) {
    // 仅红字发票返回
//      0 待处理， 1 处理中， 2 处理完成, 3 错误 仅红字发票返回
    if ("0".equals(writeOffState)) {
      return InvoiceStatusEnum.WAIT_RED;
    } else if ("1".equals(writeOffState)) {
      return InvoiceStatusEnum.RED_PROGRESSING;
    } else if ("2".equals(writeOffState)) {
      return InvoiceStatusEnum.RED_SUCCESS;
    } else if ("3".equals(writeOffState)) {
      return InvoiceStatusEnum.RED_FAIL;
    }

    throw new RuntimeException(String.format("writeOffState %s 未匹配到", writeOffState));
  }

  public static InvoiceStatusEnum mappingToInvoiceStatus(String invoiceCloudStatus) {
    // 01：蓝票申请 02：红冲申请 03：蓝票申请失败 04：红票发票失败 05：号码已分配 06：蓝票开具成功 07：红票开具成功 08：红字确认单录入中
    // WAIT-待开票; PROGRESSING-开票中 SUCCESS-开票成功; FAIL-开票失败 ; WAIT_RED-待红冲 ; RED_PROGRESSING-红冲中 RED_SUCCESS-红冲成功 RED_FAIL-红冲失败',
    if ("01".equals(invoiceCloudStatus)) {
      return InvoiceStatusEnum.PROGRESSING;
    } else if ("02".equals(invoiceCloudStatus)) {
      return InvoiceStatusEnum.RED_PROGRESSING;
    } else if ("03".equals(invoiceCloudStatus)) {
      return InvoiceStatusEnum.FAIL;
    } else if ("04".equals(invoiceCloudStatus)) {
      return InvoiceStatusEnum.RED_FAIL;
    } else if ("05".equals(invoiceCloudStatus)) {
      return null;
    } else if ("06".equals(invoiceCloudStatus)) {
      return InvoiceStatusEnum.SUCCESS;
    } else if ("07".equals(invoiceCloudStatus)) {
      return InvoiceStatusEnum.RED_SUCCESS;
    } else if ("08".equals(invoiceCloudStatus)) {
      return InvoiceStatusEnum.RED_PROGRESSING;
    }

    throw new RuntimeException(String.format("invoiceCloudStatus %s 未匹配到", invoiceCloudStatus));
  }
}
