package com.yxt.invoice.application.service.impl;

import com.google.common.base.Preconditions;
import com.yxt.domain.order.order_query.req.B2cOrderAllDetailReq;
import com.yxt.domain.order.order_query.req.B2cOrderPageSearchReq;
import com.yxt.domain.order.order_query.req.OrderPageSearchReq;
import com.yxt.domain.order.order_query.req.OrderSearchByOrderNoReq;
import com.yxt.domain.order.order_query.req.OrderSearchByOrderNoReqInvoice;
import com.yxt.domain.order.order_query.res.B2cOmsOrderInfo;
import com.yxt.domain.order.order_query.res.B2cOrderAllDetailRes;
import com.yxt.domain.order.order_query.res.B2cOrderSimpleRes;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedRes;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedResInvoice;
import com.yxt.domain.order.order_query.res.OrderSimpleRes;
import com.yxt.domain.order.refund_query.req.B2cRefundAllDetailReq;
import com.yxt.domain.order.refund_query.req.B2cRefundPageSearchReq;
import com.yxt.domain.order.refund_query.req.RefundPageSearchReq;
import com.yxt.domain.order.refund_query.req.RefundSearchByRefundNoReq;
import com.yxt.domain.order.refund_query.res.B2cRefundAllDetailRes;
import com.yxt.domain.order.refund_query.res.B2cRefundOrderInfo;
import com.yxt.domain.order.refund_query.res.B2cRefundSimpleRes;
import com.yxt.domain.order.refund_query.res.RefundDomainRelatedRes;
import com.yxt.domain.order.refund_query.res.RefundSimpleRes;
import com.yxt.invoice.application.service.InvoiceApplicationService;
import com.yxt.invoice.application.service.convert.InvoiceAmountConvert;
import com.yxt.invoice.application.service.impl.builder.AbstractApplyInvoiceCommandBuilder;
import com.yxt.invoice.application.third.order.feign.B2COrderQueryDomainApiFeign;
import com.yxt.invoice.application.third.order.feign.B2CRefundQueryDomainApiFeign;
import com.yxt.invoice.application.third.order.feign.OfflineOrderManageQueryApiFeign;
import com.yxt.invoice.application.third.order.feign.OfflineOrderQueryApiFeign;
import com.yxt.invoice.application.third.order.feign.OrderQueryDomainApiFeign;
import com.yxt.invoice.application.third.order.feign.RefundQueryDomainApiFeign;
import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.command.ExistsOrderInvoiceCommand;
import com.yxt.invoice.domain.command.ExistsThirdOrderInvoiceCommand;
import com.yxt.invoice.domain.command.QueryInvoiceDetailCommand;
import com.yxt.invoice.domain.command.QueryInvoiceListCommand;
import com.yxt.invoice.domain.command.RedCreditInvoiceCommand;
import com.yxt.invoice.domain.factory.InvoiceAggregateFactory;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ExistsOrderInvoice;
import com.yxt.invoice.domain.model.valueobject.InvoiceAmount;
import com.yxt.invoice.domain.repository.InvoiceRepository;
import com.yxt.invoice.domain.repository.ProviderInvoiceRepository;
import com.yxt.invoice.infrastructure.message.InvoiceDomainEventProducer;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.common.base_order_dto.OrderInfo;
import com.yxt.order.open.message.offline_order.model.OfflineOrderModel;
import com.yxt.order.open.message.offline_order.model.OfflineRefundOrderModel;
import com.yxt.order.open.sdk.offline_order.req.OfflineOrderDetailQueryReqDto;
import com.yxt.order.open.sdk.offline_order.req.OfflineRefundOrderDetailQueryReqDto;
import com.yxt.order.open.sdk.offline_order.req.manage.IOfflineOrderListReq;
import com.yxt.order.open.sdk.offline_order.req.manage.IOfflineRefundOrderListReq;
import com.yxt.order.open.sdk.offline_order.res.manage.IOfflineOrderRes;
import com.yxt.order.open.sdk.offline_order.res.manage.IOfflineRefundOrderRes;
import com.yxt.order.types.invoice.enums.InvoiceTransactionChannelEnum;
import com.yxt.order.types.offline.OfflineThirdOrderNo;
import com.yxt.order.types.order.enums.OrderServiceModeEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 发票应用服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Slf4j
@Service
public class InvoiceApplicationServiceImpl implements InvoiceApplicationService {

    @Resource
    private List<AbstractApplyInvoiceCommandBuilder> abstractApplyInvoiceCommandBuilderList;


    @Resource
    private B2COrderQueryDomainApiFeign b2COrderQueryDomainApiFeign;

    @Resource
    private B2CRefundQueryDomainApiFeign b2CRefundQueryDomainApiFeign;

    @Resource
    private OfflineOrderManageQueryApiFeign offlineOrderManageQueryApiFeign;

    @Resource
    private OfflineOrderQueryApiFeign offlineOrderQueryApiFeign;

    @Resource
    private OrderQueryDomainApiFeign orderQueryDomainApiFeign;

    @Resource
    private RefundQueryDomainApiFeign refundQueryDomainApiFeign;



    @Resource
    private InvoiceRepository invoiceRepository;

    @Resource
    private ProviderInvoiceRepository providerInvoiceRepository;

    @Resource
    private InvoiceDomainEventProducer invoiceDomainEventProducer;

    @Resource
    private InvoiceAggregateFactory invoiceAggregateFactory;


    @Override
    public List<InvoiceAggregate> applyInvoice(ApplyInvoiceCommand command) {
        String orderNo = command.getInvoiceMain().getOrderNo().getOrderNo();

        List<InvoiceAggregate> invoiceAggregate = invoiceRepository.findByOrderNo(orderNo);
        if (invoiceAggregate != null) {
            return invoiceAggregate;
        }

        for (AbstractApplyInvoiceCommandBuilder builder : abstractApplyInvoiceCommandBuilderList) {
            if(builder.route(command)){
                command = builder.build(command);
                break;
            }
        }

        try {
            // 命令转模型
            InvoiceAggregate aggravate = invoiceAggregateFactory.createAggravate(command);
            ExistsOrderInvoiceCommand existsOrderInvoiceCommand = aggravate.createExistsOrderInvoiceCommand();
            // 从db和远程校验发票是否已申请
            if (invoiceRepository.checkExistsFromDbAndRemote(existsOrderInvoiceCommand)) {
                aggravate.existsApply();
                invoiceDomainEventProducer.sendDomainEvents(aggravate.getDomainEvents());
                return Collections.singletonList(aggravate);
            }
            // 入库
            invoiceRepository.doSave(aggravate);
            //调用三方申请
            boolean b = providerInvoiceRepository.applyProviderInvoice(aggravate);
            if (!b) {
                log.error("applyProviderInvoice error  发票ID：{}, 开票单号：{}", aggravate.getInvoiceMain().getId(), aggravate.getInvoiceMain().getInvoiceMainNo());
            }
            //  发送领域事件
            invoiceDomainEventProducer.sendDomainEvents(aggravate.getDomainEvents());

            log.info("申请开票成功，发票ID：{}, 开票单号：{}", aggravate.getInvoiceMain().getId(), aggravate.getInvoiceMain().getInvoiceMainNo());
            return Collections.singletonList(aggravate);
        } catch (Exception e) {
            log.error("申请开票失败，订单号：{}, 错误：{}", command.getInvoiceMain().getOrderNo(), e.getMessage(), e);
            throw e;
        }

    }





    @Override
    public InvoiceAggregate applyRedCreditInvoice(RedCreditInvoiceCommand command) {
        log.info("开始红冲发票，开票单号：{}, 操作人ID：{}", command.getInvoiceMainNo(), command.getOperatorUserId());

        try {

            // 1. 查询发票聚合根
            InvoiceAggregate aggregate = invoiceRepository.findByInvoiceMainNo(command.getInvoiceMainNo());
            Preconditions.checkArgument(aggregate != null, "开票单不存在");
            Preconditions.checkArgument(aggregate.isAllowRedInvoice(), "非蓝票且开具成功发票,不允许红冲");
            InvoiceAggregate redCreditInvoiceAggregate = invoiceAggregateFactory.redCreditAggregate(aggregate, command);
            // 4. 保存更新
            redCreditInvoiceAggregate = invoiceRepository.doSaveRedInvoice(aggregate, redCreditInvoiceAggregate);


            providerInvoiceRepository.applyProviderRedInvoice(aggregate,redCreditInvoiceAggregate);

            //  发送领域事件
            invoiceDomainEventProducer.sendDomainEvents(redCreditInvoiceAggregate.getDomainEvents());

            log.info("红冲发票成功，开票单号：{}", redCreditInvoiceAggregate.getInvoiceMain().getInvoiceMainNo());

            // 6. 返回聚合根
            return redCreditInvoiceAggregate;

        } catch (Exception e) {
            log.error("红冲发票失败，开票单号：{}, 错误：{}", command.getInvoiceMainNo(), e.getMessage(), e);
            throw e; // 重新抛出异常，由控制器处理
        }
    }


    @Override
    public ExistsOrderInvoice queryOrderExistsInvoice(ExistsOrderInvoiceCommand command) {
        String orderNo = command.getOrderNo();
        String transactionChannel = command.getTransactionChannel();
        String businessType = command.getBusinessType();
        List<InvoiceAggregate> invoiceAggregateList = invoiceRepository.findByOrderNo(orderNo);
        if (!invoiceAggregateList.isEmpty()) {
            return ExistsOrderInvoice.getHasExistsOrderInvoice(invoiceAggregateList);
        }
        ExistsOrderInvoice existsOrderInvoice = new ExistsOrderInvoice();
        if (transactionChannel.equals(InvoiceTransactionChannelEnum.OFFLINE.getCode())) {
            OfflineOrderDetailQueryReqDto queryReqDto = new OfflineOrderDetailQueryReqDto();
            queryReqDto.setOrderNo(orderNo);
            //查询线下单补充
            ResponseBase<OfflineOrderModel> offlineOrderModelResponseBase = offlineOrderQueryApiFeign.detail(queryReqDto);
            Preconditions.checkArgument(offlineOrderModelResponseBase.checkSuccess(), "查询线下单数据异常,暂不支持开票");
            OfflineOrderModel data = offlineOrderModelResponseBase.getData();
            IOfflineRefundOrderListReq offlineRefundOrderListReq = new IOfflineRefundOrderListReq();
            offlineRefundOrderListReq.setOrderNo(orderNo);
            ResponseBase<PageDTO<IOfflineRefundOrderRes>> pageDTOResponseBase = offlineOrderManageQueryApiFeign.offlineRefundOrderList(offlineRefundOrderListReq);
            Preconditions.checkArgument(pageDTOResponseBase.checkSuccess(), "查询线下单退单数据异常,暂不支持开票");
            List<IOfflineRefundOrderRes> refundDataTempList = pageDTOResponseBase.getData().getData();
            List<OfflineRefundOrderModel> refundDataList = new ArrayList<>();

            for (IOfflineRefundOrderRes iOfflineRefundOrderRes : refundDataTempList) {
                OfflineRefundOrderDetailQueryReqDto refundDetailQueryReqDto = new OfflineRefundOrderDetailQueryReqDto();
                refundDetailQueryReqDto.setRefundNo(iOfflineRefundOrderRes.getRefundNo());
                ResponseBase<OfflineRefundOrderModel> offlineRefundOrderModelResponseBase = offlineOrderQueryApiFeign.refundDetail(refundDetailQueryReqDto);
                Preconditions.checkArgument(offlineRefundOrderModelResponseBase.checkSuccess(), "查询线下单退单数据异常,暂不支持开票");
                Preconditions.checkArgument(!offlineRefundOrderModelResponseBase.getData().getBaseRefundInfo().getRefundTypeValue().equals("ALL"), "暂不支持整单退开票");
                refundDataList.add(offlineRefundOrderModelResponseBase.getData());
            }
            List<InvoiceAmount> invoiceAmountList = InvoiceAmountConvert.convertByOfflineOrder(data, refundDataList);
            existsOrderInvoice.setInvoiceAmounts(invoiceAmountList);
            existsOrderInvoice.setThirdPlatformCode(data.getBaseOrderInfo().getThirdPlatformCodeValue());
            existsOrderInvoice.setThirdOrderNo(data.getBaseOrderInfo().getThirdOrderNo().getThirdOrderNo());
            existsOrderInvoice.setOrderNo(data.getBaseOrderInfo().getOrderNo().getOrderNo());
            existsOrderInvoice.setPosNo(data.getBaseOrderInfo().getThirdOrderNo().getThirdOrderNo());
            existsOrderInvoice.setTransactionChannel(InvoiceTransactionChannelEnum.OFFLINE.getCode());
            existsOrderInvoice.setBusinessType(OrderServiceModeEnum.O2O.getCode());
            return existsOrderInvoice;

        } else {
            if (businessType.equals(OrderServiceModeEnum.O2O.getCode())) {
                OrderSearchByOrderNoReqInvoice req = new OrderSearchByOrderNoReqInvoice();
                req.setOrderNo(orderNo);
                ResponseBase<OrderDomainRelatedResInvoice> orderDomainRelatedResResponseBase = orderQueryDomainApiFeign.orderSearchByOrderNoInvoice(req);
                Preconditions.checkArgument(orderDomainRelatedResResponseBase.checkSuccess(), "查询线上单O2O数据异常,暂不支持开票");
                OrderDomainRelatedResInvoice data = orderDomainRelatedResResponseBase.getData();
                OrderInfo orderInfo = data.getOrderInfo();
                Preconditions.checkArgument(orderInfo.getErpState() == 100, "订单下账状态,暂不支持开票");
                Preconditions.checkArgument(orderInfo.getOrderState() != 101 && orderInfo.getOrderState() != 102, "订单状态,暂不支持开票");
                Preconditions.checkArgument(StringUtils.isNotEmpty(orderInfo.getErpSaleNo()), "订单销售流水信息不全,暂不支持开票");
                RefundPageSearchReq refundPageSearchReq = new RefundPageSearchReq();
                refundPageSearchReq.setOrderNoList(Collections.singletonList(orderNo));
                ResponseBase<PageDTO<RefundSimpleRes>> pageDTOResponseBase = refundQueryDomainApiFeign.refundSearchPage(refundPageSearchReq);
                Preconditions.checkArgument(pageDTOResponseBase.checkSuccess(), "订单销售流水信息不全,暂不支持开票");

                List<RefundDomainRelatedRes> refundDataList = new ArrayList<>();
                List<RefundSimpleRes> tempRefundList = pageDTOResponseBase.getData().getData();
                for (RefundSimpleRes dataRefund : tempRefundList) {
                    RefundSearchByRefundNoReq refundSearchByRefundNoReq = new RefundSearchByRefundNoReq();
                    refundSearchByRefundNoReq.setRefundNo(dataRefund.getRefundNo());
                    ResponseBase<RefundDomainRelatedRes> refundDomainRelatedResResponseBase = refundQueryDomainApiFeign.refundSearchByRefundNo(refundSearchByRefundNoReq);
                    Preconditions.checkArgument(refundDomainRelatedResResponseBase.checkSuccess(), "查询退款单失败,暂不支持开票");
                    RefundDomainRelatedRes dataRefundTemp = refundDomainRelatedResResponseBase.getData();
                    if (dataRefundTemp.getRefundOrder().getState() == 102 || dataRefundTemp.getRefundOrder().getState() == 103) {
                        continue;
                    }
                    Preconditions.checkArgument(dataRefundTemp.getRefundOrder().getState() == 100, "退款单状态,暂不支持开票");
                    Preconditions.checkArgument(dataRefundTemp.getRefundOrder().getErpState() == 100, "退款单下账状态,暂不支持开票");
                    refundDataList.add(dataRefundTemp);
                }
                List<InvoiceAmount> invoiceAmountList = InvoiceAmountConvert.convertByOnlineOrderO2O(data, refundDataList);

                existsOrderInvoice.setInvoiceAmounts(invoiceAmountList);
                existsOrderInvoice.setThirdPlatformCode(orderInfo.getThirdPlatformCode());
                existsOrderInvoice.setThirdOrderNo(orderInfo.getThirdOrderNo());
                existsOrderInvoice.setOrderNo(String.valueOf(orderInfo.getOrderNo()));
                existsOrderInvoice.setPosNo(orderInfo.getErpSaleNo());
                existsOrderInvoice.setTransactionChannel(InvoiceTransactionChannelEnum.ONLINE.getCode());
                existsOrderInvoice.setBusinessType(OrderServiceModeEnum.O2O.getCode());
                return existsOrderInvoice;
            } else {
                B2cOrderAllDetailReq req = new B2cOrderAllDetailReq();
                req.setOmsOrderNo(orderNo);
                ResponseBase<B2cOrderAllDetailRes> allDetailResResponseBase = b2COrderQueryDomainApiFeign.orderSearchByOrderNo(req);
                Preconditions.checkArgument(allDetailResResponseBase.checkSuccess(), "查询线上单B2C数据异常,暂不支持开票");
                B2cOrderAllDetailRes allDetailRes = allDetailResResponseBase.getData();
                B2cOmsOrderInfo orderInfo = allDetailRes.getOmsOrderInfo();

                Preconditions.checkArgument(orderInfo.getErpStatus() == 100, "订单下账状态,暂不支持开票");
                Preconditions.checkArgument(orderInfo.getOrderStatus() != 101 && orderInfo.getOrderStatus() != 102, "订单状态,暂不支持开票");
                B2cRefundPageSearchReq refundPageSearchReq = new B2cRefundPageSearchReq();
                refundPageSearchReq.setThirdOrderNos(Collections.singletonList(orderInfo.getThirdOrderNo()));
                ResponseBase<PageDTO<B2cRefundSimpleRes>> pageDTOResponseBase = b2CRefundQueryDomainApiFeign.refundSearchPage(refundPageSearchReq);
                Preconditions.checkArgument(pageDTOResponseBase.checkSuccess(), "订单销售流水信息不全,暂不支持开票");

                List<B2cRefundAllDetailRes> refundDataList = new ArrayList<>();
                List<B2cRefundSimpleRes> tempRefundList = pageDTOResponseBase.getData().getData();
                for (B2cRefundSimpleRes dataRefund : tempRefundList) {
                    B2cRefundAllDetailReq refundSearchByRefundNoReq = new B2cRefundAllDetailReq();
                    refundSearchByRefundNoReq.setRefundNo(String.valueOf(dataRefund.getRefundNo()));
                    ResponseBase<B2cRefundAllDetailRes> refundSearchByRefundNoResResponseBase = b2CRefundQueryDomainApiFeign.refundSearchByRefundNo(refundSearchByRefundNoReq);
                    Preconditions.checkArgument(refundSearchByRefundNoResResponseBase.checkSuccess(), "查询退款单失败,暂不支持开票");
                    B2cRefundAllDetailRes refundAllDetailRes = refundSearchByRefundNoResResponseBase.getData();
                    B2cRefundOrderInfo dataRefundTemp = refundAllDetailRes.getRefundOrderInfo();
                    if (Objects.equals(dataRefundTemp.getState(), "102") || Objects.equals(dataRefundTemp.getState(), "103")) {
                        continue;
                    }
                    Preconditions.checkArgument(Objects.equals(dataRefundTemp.getState(), "100"), "退款单状态,暂不支持开票");
                    Preconditions.checkArgument(dataRefundTemp.getErpState() == 100, "退款单下账状态,暂不支持开票");
                    refundDataList.add(refundAllDetailRes);
                }
                List<InvoiceAmount> invoiceAmountList = InvoiceAmountConvert.convertByOnlineOrderB2C(allDetailRes, refundDataList);
                existsOrderInvoice.setInvoiceAmounts(invoiceAmountList);
                existsOrderInvoice.setThirdPlatformCode(orderInfo.getThirdPlatformCode());
                existsOrderInvoice.setThirdOrderNo(orderInfo.getThirdOrderNo());
                existsOrderInvoice.setOrderNo(String.valueOf(orderInfo.getOrderNo()));
                existsOrderInvoice.setPosNo(orderInfo.getErpSaleNo());
                existsOrderInvoice.setTransactionChannel(InvoiceTransactionChannelEnum.ONLINE.getCode());
                existsOrderInvoice.setBusinessType(OrderServiceModeEnum.B2C.getCode());
                return existsOrderInvoice;
            }
        }

    }

    @Override
    public ExistsOrderInvoice queryThirdOrderExistsInvoiceReqDto(ExistsThirdOrderInvoiceCommand command) {
        String thirdOrderNo = command.getThirdOrderNo();
        IOfflineOrderListReq queryReqDto = new IOfflineOrderListReq();
        queryReqDto.setThirdOrderNo(thirdOrderNo);
        ExistsOrderInvoiceCommand queryCommand = null;
        //查询线下单补充
        if (null == queryCommand) {
            ResponseBase<PageDTO<IOfflineOrderRes>> pageDTOResponseBase = offlineOrderManageQueryApiFeign.offlineOrderList(queryReqDto);
            if (pageDTOResponseBase.checkSuccess() && null != pageDTOResponseBase.getData() && !pageDTOResponseBase.getData().getData().isEmpty()) {
                IOfflineOrderRes iOfflineOrderRes = pageDTOResponseBase.getData().getData().get(0);
                queryCommand = new ExistsOrderInvoiceCommand();
                queryCommand.setOrderNo(iOfflineOrderRes.getOrderNo());
                queryCommand.setTransactionChannel(InvoiceTransactionChannelEnum.OFFLINE.getCode());
                queryCommand.setBusinessType(OrderServiceModeEnum.O2O.getCode());
                queryCommand.setPosNo(iOfflineOrderRes.getThirdOrderNo());
                queryCommand.setThirdPlatformCode(iOfflineOrderRes.getThirdPlatformCode());
                queryCommand.setThirdOrderNo(OfflineThirdOrderNo.thirdOrderNo(iOfflineOrderRes.getThirdOrderNo()));
                queryCommand.setStoreCode(iOfflineOrderRes.getStoreCode());
            }
        }
        //查询线上单O2O补充
        if (null == queryCommand) {
            OrderPageSearchReq searchParam = new OrderPageSearchReq();
            searchParam.setThirdOrderNoList(Collections.singletonList(thirdOrderNo));
            ResponseBase<PageDTO<OrderSimpleRes>> pageDTOResponseBase = orderQueryDomainApiFeign.orderSearchPage(searchParam);
            if (pageDTOResponseBase.checkSuccess() && null != pageDTOResponseBase.getData() && !pageDTOResponseBase.getData().getData().isEmpty()) {
                OrderSimpleRes iOfflineOrderRes = pageDTOResponseBase.getData().getData().get(0);
                queryCommand = new ExistsOrderInvoiceCommand();
                queryCommand.setOrderNo(iOfflineOrderRes.getOrderNo());
                queryCommand.setTransactionChannel(InvoiceTransactionChannelEnum.ONLINE.getCode());
                queryCommand.setBusinessType(OrderServiceModeEnum.O2O.getCode());
                queryCommand.setPosNo(iOfflineOrderRes.getThirdOrderNo());
                queryCommand.setThirdPlatformCode(iOfflineOrderRes.getThirdPlatformCode());
                queryCommand.setThirdOrderNo(OfflineThirdOrderNo.thirdOrderNo(iOfflineOrderRes.getThirdOrderNo()));
                queryCommand.setStoreCode(iOfflineOrderRes.getOrganizationCode());
            }
        }

        //查询线上单B2C补充
        if (null == queryCommand) {
            B2cOrderPageSearchReq searchParam = new B2cOrderPageSearchReq();
            searchParam.setThirdOrderNos(Collections.singletonList(thirdOrderNo));
            ResponseBase<PageDTO<B2cOrderSimpleRes>> pageDTOResponseBase = b2COrderQueryDomainApiFeign.orderSearchPage(searchParam);
            if (pageDTOResponseBase.checkSuccess() && null != pageDTOResponseBase.getData() && !pageDTOResponseBase.getData().getData().isEmpty()) {
                B2cOrderSimpleRes iOfflineOrderRes = pageDTOResponseBase.getData().getData().get(0);
                queryCommand = new ExistsOrderInvoiceCommand();
                queryCommand.setOrderNo(String.valueOf(iOfflineOrderRes.getOrderNo()));
                queryCommand.setTransactionChannel(InvoiceTransactionChannelEnum.ONLINE.getCode());
                queryCommand.setBusinessType(OrderServiceModeEnum.B2C.getCode());
                queryCommand.setPosNo(iOfflineOrderRes.getThirdOrderNo());
                queryCommand.setThirdPlatformCode(iOfflineOrderRes.getThirdPlatformCode());
                queryCommand.setThirdOrderNo(OfflineThirdOrderNo.thirdOrderNo(iOfflineOrderRes.getThirdOrderNo()));
                queryCommand.setStoreCode(iOfflineOrderRes.getOnlineStoreCode());
            }

        }

        Preconditions.checkArgument(queryCommand != null, "此订单非一心堂订单,请核对信息");
        return queryOrderExistsInvoice(queryCommand);
    }

    @Override
    @Transactional(readOnly = true)
    public PageDTO<InvoiceMain> queryInvoiceList(QueryInvoiceListCommand query) {
        try {
            return invoiceRepository.findInvoiceManByConditions(query);
        } catch (Exception e) {
            log.error("查询发票列表失败，错误：{}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public InvoiceAggregate queryInvoiceDetail(QueryInvoiceDetailCommand query) {
        log.info("查询发票详情，开票单号：{}", query.getInvoiceMainNo());
        try {
            // 查询发票聚合根
            InvoiceAggregate aggregate = invoiceRepository.findByInvoiceMainNo(query.getInvoiceMainNo());
            Preconditions.checkArgument(aggregate != null, "开票单号不存在");
            return aggregate;
        } catch (Exception e) {
            log.error("查询发票详情失败，错误：{}", e.getMessage(), e);
            throw e;
        }
    }


}
