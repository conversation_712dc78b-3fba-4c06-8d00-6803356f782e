package com.yxt.invoice.infrastructure.provider.dto.req;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import lombok.EqualsAndHashCode;

// 请求DTO
@EqualsAndHashCode(callSuper = true)
@Data
public class PostNegativeInvoiceIssueReqDto extends TaxCloudBaseRequest {

    private NegativeInvoiceData data;

    // 内部类 - 红字发票数据
    @Data
    public static class NegativeInvoiceData {
        /**
         * 录入方身份
         * 0：销方
         * 1：购方
         */
        private String inputType;

        /**
         * 销售门店
         */
        private String sellerNumber;

        /**
         * 是否整单
         *  Y 是
         *  N 否
         */
        private String isEntire;

        /**
         * 纳税人识别号
         */
        private String sellerTin;
        /**
         * 外部业务唯一流水号
         */
        private String outRequestCode;

        /**
         * 原蓝字外部业务唯一流水号
         */
        private String originalOutRequestCode;
        /**
         * 蓝字发票业务流水号
         */
        private String responseId;
        /**
         * 冲红原因
         * 01：开票有误
         * 02：销货退回
         * 03：服务中止
         * 04：销售折让
         */
        private String writeOffReason;
        /**
         * 原蓝字发票号码
         */
        private String originalInvoiceCode;
        /**
         * 冲红金额（负数）
         */
        private BigDecimal invoiceAmount;
        /**
         * 冲红税额（负数）
         */
        private BigDecimal taxAmount;
        private List<NegativeInvoiceItem> itemList;

        // Getters and Setters
    }

    // 内部类 - 红字发票明细
    @Data
    public static class NegativeInvoiceItem {
        /**
         * 行号
         */
        private Integer line;
        /**
         * 原蓝字发票明细行号
         */
        private Integer positiveLine;
        /**
         * 行金额（负数）
         */
        private BigDecimal amount;
        /**
         * 行税额（负数）
         */
        private BigDecimal taxAmount;
        /**
         * 税率
         */
        private BigDecimal taxRate;
        /**
         * 冲红数量（负数）
         */
        private BigDecimal invoiceQty;
        /**
         * 冲销项目名称
         * 货物或应税劳务 拼装规
         * 则：“*商品服务简称
         * （ spfwjc ）*”+“项目
         * 名称（xmmc）”
         */
        private String itemName;
        /**
         * 单价（正数）
         */
        private String itemPrice;
        /**
         * 商品编码
         */
        private String materialId;
        /**
         * 规格型号
         */
        private String specification;
        /**
         * 商品服务简称
         */
        private String spfwjc;
        /**
         * 税收分类编码
         */
        private String taxonomyCode;
        /**
         * 单位
         */
        private String unit;
        /**
         * 项目名称
         */
        private String xmmc;

        // Getters and Setters
    }

    // Getters and Setters
}
