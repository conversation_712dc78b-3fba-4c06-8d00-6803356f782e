package com.yxt.invoice.infrastructure.provider.dto.req;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import lombok.EqualsAndHashCode;

// 请求DTO
@EqualsAndHashCode(callSuper = true)
@Data
public class PositiveInvoiceIssueReqDto extends TaxCloudBaseRequest {

    private List<InvoiceData> data;

    // 内部类 - 发票主数据
    @Data
    public static class InvoiceData {
        /**
         * 外部业务唯一请求号
         */
        private String outRequestCode;
        /**
         * 是否含税
         * Y:是
         * N:否
         */
        private String inTaxRateTag;
        /**
         * 发票类型
         * 01：数电专票
         * 02：数电普票
         */
        private String invoiceType;
        /**
         * 开票对象：（是否自然人）
         * Y：自然人
         * N：非自然人（公司）
         */
        private String buyerType;
        /**
         * 购方名称
         */
        private String buyerName;
        /**
         * 购方纳税人识别号\个人
         * 身份证号
         */
        private String buyerTin;

        /**
         * 购方地址
         */
        private String buyerAddress;

        /**
         * 购方电话
         */
        private String buyerPhone;
        /**
         * 购方银行
         */
        private String buyerBank;
        /**
         * 购方银行帐号
         */
        private String buyerBankAccount;

        /**
         * 购方邮箱
         */
        private String buyerEmail;
        /**
         * 购方手机号
         */
        private String buyerMobile;
        /**
         * 显示购方银行帐号
         * Y：显示
         * N：不显示
         */
        private String showBuyerBankAccount;
        /**
         * 实际开具的纳税人名称
         */
        private String realitySellerName;
        /**
         * 实际开具门店编码
         */
        private String realitySellerNumber;
        /**
         * 开票公司/门店编码
         */
        private String sellerNumber;
        /**
         * 开票纳税人识别号
         */
        private String sellerTin;
        /**
         * 开票纳税人名称
         */
        private String sellerName;
        /**
         * 地址
         */
        private String sellerAddress;

        /**
         * 电话
         */
        private String sellerPhone;
        /**
         * 开户银行
         */
        private String sellerBank;
        /**
         * 银行帐号
         */
        private String sellerBankAccount;


        /**
         * 开票人
         */
        private String operator;
        /**
         * 开票请求日期
         */
        private String requestDate;
        /**
         * 金额
         */
        private BigDecimal invoiceAmount;
        /**
         * 税额
         */
        private BigDecimal taxAmount;
        /**
         * 价税合计
         */
        private BigDecimal priceTaxAmount;
        /**
         * 是否拆票
         * Y：拆票
         * N：不拆票
         */
        private String splitBill;
        /**
         * 备注
         */
        private String notes;
        /**
         * 收款人
         */
        private String payee;
        /**
         * 复核人
         */
        private String reviewedBy;
        /**
         * 特定要素
         */
        private String specificElements;
        /**
         * 购方手机号
         */
        private List<InvoiceItem> itemList;

        // Getters and Setters
    }

    // 内部类 - 发票明细行
    @Data
    public static class InvoiceItem {
        /**
         * 行号
         */
        private Long line;
        /**
         * 商品服务简称(税收分类
         * 编码父级分类名称)
         */
        private String spfwjc;
        /**
         * 项目名称（商品名称）
         */
        private String xmmc;
        /**
         * 货物或应税劳务 拼装规
         * 则：“*商品服务简称
         * （ spfwjc ）*”+“项目
         * 名称（xmmc）”
         */
        private String itemName;
        /**
         * 规格
         */
        private String specification;
        /**
         * 单位
         */
        private String unit;
        /**
         * 可开票总数量
         */
        private BigDecimal quantity;
        /**
         * 本次开票数量
         */
        private BigDecimal invoiceQty;
        /**
         * 单价
         */
        private String itemPrice;
        /**
         * 行金额
         */
        private BigDecimal amount;
        /**
         * 行税额行金额/(1+税率)*
         * 税率
         */
        private BigDecimal taxAmount;
        /**
         * 行税率
         */
        private BigDecimal taxRate;
        /**
         * 行价税合计
         */
        private BigDecimal inTaxAmount;
        /**
         * 税收分类编码
         */
        private String taxonomyCode;
        /**
         * 行性质
         * 00：正常行；
         * 01：折扣行；
         * 02：被折扣行
         * 折扣行紧跟被折扣行之
         * 后。
         */
        private String invoiceLineType;
        /**
         * 扣除额
         */
        private BigDecimal deductAmount;
        /**
         * 启动优惠政策
         * Y：启用；
         * N：不启用
         */
        private String policyStatus;
        /**
         * 优惠标识
         * N（启用时优
         * 惠政策时必
         * 填）
         */
        private String policyTag;
        /**
         * 优惠税率
         * N（启用时优
         * 惠政策时必
         * 填）
         */
        private String policyTaxRate;
        /**
         * 商品编码
         */
        private String materialId;


        /**
         *
         */
        private String flag;

        // Getters and Setters
    }

    // Getters and Setters
}