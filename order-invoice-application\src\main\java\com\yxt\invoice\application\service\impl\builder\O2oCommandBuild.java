package com.yxt.invoice.application.service.impl.builder;

import com.google.common.base.Preconditions;
import com.yxt.domain.order.order_query.req.OrderSearchByOrderNoReq;
import com.yxt.domain.order.order_query.req.OrderSearchByOrderNoReqInvoice;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedRes;
import com.yxt.domain.order.order_query.res.OrderDomainRelatedResInvoice;
import com.yxt.domain.order.refund_query.req.RefundPageSearchReq;
import com.yxt.domain.order.refund_query.req.RefundSearchByRefundNoReq;
import com.yxt.domain.order.refund_query.res.RefundDomainRelatedRes;
import com.yxt.domain.order.refund_query.res.RefundSimpleRes;
import com.yxt.invoice.application.service.convert.InvoiceConvert;
import com.yxt.invoice.application.third.baseinfo.feign.BaseInfoClient;
import com.yxt.invoice.application.third.baseinfo.feign.MemberClient;
import com.yxt.invoice.application.third.goods.dto.req.AveragePriceQuery;
import com.yxt.invoice.application.third.goods.dto.res.AveragePriceVO;
import com.yxt.invoice.application.third.goods.feign.MiddleMerchandiseClient;
import com.yxt.invoice.application.third.order.feign.OrderQueryDomainApiFeign;
import com.yxt.invoice.application.third.order.feign.RefundQueryDomainApiFeign;
import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.middle.baseinfo.res.org.BizUnitOrgResDTO;
import com.yxt.middle.baseinfo.res.store.StoreInfoDataResDTO;
import com.yxt.middle.member.res.member.MemberInfoVo;
import com.yxt.order.common.base_order_dto.OrderDetail;
import com.yxt.order.common.base_order_dto.OrderInfo;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTransactionChannelEnum;
import com.yxt.order.types.order.enums.ErpStateEnum;
import com.yxt.order.types.order.enums.OrderStateEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class O2oCommandBuild extends AbstractApplyInvoiceCommandBuilder {


  @Resource
  private OrderQueryDomainApiFeign orderQueryDomainApiFeign;

  @Resource
  private RefundQueryDomainApiFeign refundQueryDomainApiFeign;


  @Resource
  private MiddleMerchandiseClient middleMerchandiseClient;

  @Resource
  private BaseInfoClient baseInfoClient;
  @Resource
  private MemberClient memberClient;

  @Override
  public Boolean route(ApplyInvoiceCommand command) {
    InvoiceMain invoiceMain = command.getInvoiceMain();
    return invoiceMain.getTransactionChannel().equals(InvoiceTransactionChannelEnum.ONLINE.name())
        && invoiceMain.getBusinessType().equals(InvoiceBusinessTypeEnum.O2O.name());
  }

  @Override
  public ApplyInvoiceCommand build(ApplyInvoiceCommand command) {
    return extractedByOnlineO2O(command);
  }

  private ApplyInvoiceCommand extractedByOnlineO2O(ApplyInvoiceCommand command) {
    InvoiceMain invoiceMain = command.getInvoiceMain();
    String orderNo = invoiceMain.getOrderNo().getOrderNo();
    String userId = invoiceMain.getUserId();
    String merCode = invoiceMain.getMerCode();

    OrderSearchByOrderNoReqInvoice req = new OrderSearchByOrderNoReqInvoice();
    req.setOrderNo(orderNo);
    ResponseBase<OrderDomainRelatedResInvoice> orderResponse = orderQueryDomainApiFeign.orderSearchByOrderNoInvoice(
        req);
    Preconditions.checkArgument(orderResponse.checkSuccess(),
        "查询线上单O2O数据异常,暂不支持开票");
    OrderDomainRelatedResInvoice data = orderResponse.getData();

    OrderInfo orderInfo = data.getOrderInfo();
    Preconditions.checkArgument(
        Objects.equals(orderInfo.getErpState(), ErpStateEnum.HAS_SALE.getCode()), String.format("订单下账状态 %s,暂不支持开票。需要为已下账",
        ErpStateEnum.getByErpState(orderInfo.getErpState()).getMsg()));
    Preconditions.checkArgument(
        !Objects.equals(orderInfo.getOrderState(), OrderStateEnum.CLOSED.getCode())
            && !Objects.equals(orderInfo.getOrderState(), OrderStateEnum.CANCEL.getCode()),
        String.format("订单状态 %s,暂不支持开票",OrderStateEnum.getOrderState(orderInfo.getOrderState()).getMsg()));
    Preconditions.checkArgument(StringUtils.isNotEmpty(orderInfo.getErpSaleNo()),
        "订单销售流水信息不全,暂不支持开票");
    RefundPageSearchReq refundPageSearchReq = new RefundPageSearchReq();
    refundPageSearchReq.setOrderNoList(Collections.singletonList(orderNo));
    ResponseBase<PageDTO<RefundSimpleRes>> pageDTOResponseBase = refundQueryDomainApiFeign.refundSearchPage(
        refundPageSearchReq);
    Preconditions.checkArgument(pageDTOResponseBase.checkSuccess(),
        "订单销售流水信息不全,暂不支持开票");

    if (StringUtils.isNotEmpty(orderInfo.getMemberNo())) {
      ResponseBase<MemberInfoVo> memberByCardNo = memberClient.getMemberByCardNo(
          orderInfo.getMemberNo());
      Preconditions.checkArgument(memberByCardNo.checkSuccess(), "查询会员信息失败,暂不支持开票");
      Preconditions.checkArgument(memberByCardNo.getData().getUserId().toString().equals(userId),
          "会员信息不一致,暂不支持开票");
    }
    List<RefundDomainRelatedRes> refundDataList = new ArrayList<>();
    List<RefundSimpleRes> tempRefundList = pageDTOResponseBase.getData().getData();
    for (RefundSimpleRes dataRefund : tempRefundList) {
      RefundSearchByRefundNoReq refundSearchByRefundNoReq = new RefundSearchByRefundNoReq();
      refundSearchByRefundNoReq.setRefundNo(dataRefund.getRefundNo());
      ResponseBase<RefundDomainRelatedRes> refundDomainRelatedResResponseBase = refundQueryDomainApiFeign.refundSearchByRefundNo(
          refundSearchByRefundNoReq);
      Preconditions.checkArgument(refundDomainRelatedResResponseBase.checkSuccess(),
          "查询退款单失败,暂不支持开票");
      RefundDomainRelatedRes dataRefundTemp = refundDomainRelatedResResponseBase.getData();
      if (dataRefundTemp.getRefundOrder().getState() == 102
          || dataRefundTemp.getRefundOrder().getState() == 103) {
        continue;
      }
      Preconditions.checkArgument(dataRefundTemp.getRefundOrder().getState() == 100,
          "退款单状态,暂不支持开票");
      Preconditions.checkArgument(dataRefundTemp.getRefundOrder().getErpState() == 100,
          "退款单下账状态,暂不支持开票");
      refundDataList.add(dataRefundTemp);
    }
    String storeCode = orderInfo.getOrganizationCode();
    List<String> erpCodeList = data.getDetailList().stream().map(OrderDetail::getErpCode).distinct()
        .collect(Collectors.toList());
    ResponseBase<List<AveragePriceVO>> detailListPriceResponse = middleMerchandiseClient.queryAveragePrice(
        DsConstants.SYSTEM, AveragePriceQuery.buildBean(merCode, storeCode, erpCodeList));
    Preconditions.checkArgument(detailListPriceResponse.checkSuccess(),
        "查询商品均价失败,暂不支持开票");
    ResponseBase<StoreInfoDataResDTO> storeInfo = baseInfoClient.getStoreInfo(merCode, null,
        storeCode);
    Preconditions.checkArgument(storeInfo.checkSuccess(), "查询门店信息失败,暂不支持开票");
    Optional<BizUnitOrgResDTO> first = storeInfo.getData().getBizUnitOrgList().stream()
        .filter(d -> "1".equals(d.getLayer())).findFirst();
    Preconditions.checkArgument(first.isPresent(), "查询门店所属公司信息失败,暂不支持开票");
    invoiceMain.setCompanyCode(first.get().getUnitCode());
    invoiceMain.setCompanyName(first.get().getUnitName());

    return InvoiceConvert.applyInvoiceConvertByOnlineOrderO2O(data, refundDataList, command,
        detailListPriceResponse.getData());
  }


}
