package com.yxt.invoice.application.service.impl.builder;

import com.google.common.base.Preconditions;
import com.yxt.domain.order.order_query.req.B2cOrderAllDetailReq;
import com.yxt.domain.order.order_query.res.B2cOmsOrderInfo;
import com.yxt.domain.order.order_query.res.B2cOrderAllDetailRes;
import com.yxt.domain.order.order_query.res.B2cOrderDetail;
import com.yxt.domain.order.refund_query.req.B2cRefundAllDetailReq;
import com.yxt.domain.order.refund_query.req.B2cRefundPageSearchReq;
import com.yxt.domain.order.refund_query.res.B2cRefundAllDetailRes;
import com.yxt.domain.order.refund_query.res.B2cRefundOrderInfo;
import com.yxt.domain.order.refund_query.res.B2cRefundSimpleRes;
import com.yxt.invoice.application.service.convert.InvoiceConvert;
import com.yxt.invoice.application.third.baseinfo.feign.BaseInfoClient;
import com.yxt.invoice.application.third.baseinfo.feign.MemberClient;
import com.yxt.invoice.application.third.goods.dto.req.AveragePriceQuery;
import com.yxt.invoice.application.third.goods.dto.res.AveragePriceVO;
import com.yxt.invoice.application.third.goods.feign.MiddleMerchandiseClient;
import com.yxt.invoice.application.third.order.feign.B2COrderQueryDomainApiFeign;
import com.yxt.invoice.application.third.order.feign.B2CRefundQueryDomainApiFeign;
import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.middle.baseinfo.res.org.BizUnitOrgResDTO;
import com.yxt.middle.baseinfo.res.store.StoreInfoDataResDTO;
import com.yxt.middle.member.res.member.MemberInfoVo;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.invoice.enums.InvoiceBusinessTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceTransactionChannelEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class B2cCommandBuild extends AbstractApplyInvoiceCommandBuilder {

  @Resource
  private B2COrderQueryDomainApiFeign b2COrderQueryDomainApiFeign;

  @Resource
  private B2CRefundQueryDomainApiFeign b2CRefundQueryDomainApiFeign;

  @Resource
  private MiddleMerchandiseClient middleMerchandiseClient;

  @Resource
  private BaseInfoClient baseInfoClient;
  @Resource
  private MemberClient memberClient;

  @Override
  public Boolean route(ApplyInvoiceCommand command) {
    InvoiceMain invoiceMain = command.getInvoiceMain();
    return invoiceMain.getTransactionChannel().equals(InvoiceTransactionChannelEnum.ONLINE.name())
        && invoiceMain.getBusinessType().equals(InvoiceBusinessTypeEnum.B2C.name());
  }

  @Override
  public ApplyInvoiceCommand build(ApplyInvoiceCommand command) {
    return extractedByOnlineB2C(command);
  }

  private ApplyInvoiceCommand extractedByOnlineB2C(ApplyInvoiceCommand command) {
    InvoiceMain invoiceMain = command.getInvoiceMain();
    String orderNo = invoiceMain.getOrderNo().getOrderNo();
    String userId = invoiceMain.getUserId();
    String merCode = invoiceMain.getMerCode();

    B2cOrderAllDetailReq req = new B2cOrderAllDetailReq();
    req.setOmsOrderNo(orderNo);
    ResponseBase<B2cOrderAllDetailRes> omsOrderInfoResDtoResponseBase = b2COrderQueryDomainApiFeign.orderSearchByOrderNo(
        req);
    Preconditions.checkArgument(omsOrderInfoResDtoResponseBase.checkSuccess(),
        "查询线上单B2C数据异常,暂不支持开票");
    B2cOrderAllDetailRes allDetailRes = omsOrderInfoResDtoResponseBase.getData();
    B2cOmsOrderInfo orderInfo = allDetailRes.getOmsOrderInfo();

    Preconditions.checkArgument(orderInfo.getErpStatus() == 100, "订单下账状态,暂不支持开票");
    Preconditions.checkArgument(
        orderInfo.getOrderStatus() != 101 && orderInfo.getOrderStatus() != 102,
        "订单状态,暂不支持开票");
    B2cRefundPageSearchReq refundPageSearchReq = new B2cRefundPageSearchReq();
    refundPageSearchReq.setThirdOrderNos(Collections.singletonList(orderInfo.getThirdOrderNo()));
    ResponseBase<PageDTO<B2cRefundSimpleRes>> pageDTOResponseBase = b2CRefundQueryDomainApiFeign.refundSearchPage(
        refundPageSearchReq);
    Preconditions.checkArgument(pageDTOResponseBase.checkSuccess(),
        "订单销售流水信息不全,暂不支持开票");

    if (StringUtils.isNotEmpty(orderInfo.getMemberNo())) {
      ResponseBase<MemberInfoVo> memberByCardNo = memberClient.getMemberByCardNo(
          orderInfo.getMemberNo());
      Preconditions.checkArgument(memberByCardNo.checkSuccess(), "查询会员信息失败,暂不支持开票");
      Preconditions.checkArgument(memberByCardNo.getData().getUserId().toString().equals(userId),
          "会员信息不一致,暂不支持开票");
    }
    List<B2cRefundAllDetailRes> refundDataList = new ArrayList<>();
    List<B2cRefundSimpleRes> tempRefundList = pageDTOResponseBase.getData().getData();
    for (B2cRefundSimpleRes dataRefund : tempRefundList) {
      B2cRefundAllDetailReq refundSearchByRefundNoReq = new B2cRefundAllDetailReq();
      refundSearchByRefundNoReq.setRefundNo(String.valueOf(dataRefund.getRefundNo()));
      ResponseBase<B2cRefundAllDetailRes> refundSearchByRefundNoResResponseBase = b2CRefundQueryDomainApiFeign.refundSearchByRefundNo(
          refundSearchByRefundNoReq);
      Preconditions.checkArgument(refundSearchByRefundNoResResponseBase.checkSuccess(),
          "查询退款单失败,暂不支持开票");
      B2cRefundAllDetailRes refundAllDetailRes = refundSearchByRefundNoResResponseBase.getData();
      B2cRefundOrderInfo dataRefundTemp = refundAllDetailRes.getRefundOrderInfo();
      if (Objects.equals(dataRefundTemp.getState(), "102") || Objects.equals(
          dataRefundTemp.getState(), "103")) {
        continue;
      }
      Preconditions.checkArgument(Objects.equals(dataRefundTemp.getState(), "100"),
          "退款单状态,暂不支持开票");
      Preconditions.checkArgument(dataRefundTemp.getErpState() == 100,
          "退款单下账状态,暂不支持开票");
      refundDataList.add(refundAllDetailRes);
    }
    String storeCode = orderInfo.getOrganizationCode();
    List<String> erpCodeList = allDetailRes.getOrderDetailList().stream()
        .map(B2cOrderDetail::getErpCode).distinct().collect(Collectors.toList());
    ResponseBase<List<AveragePriceVO>> detailListPriceResponse = middleMerchandiseClient.queryAveragePrice(
        DsConstants.SYSTEM, AveragePriceQuery.buildBean(merCode, storeCode, erpCodeList));
    Preconditions.checkArgument(detailListPriceResponse.checkSuccess(),
        "查询商品均价失败,暂不支持开票");
    ResponseBase<StoreInfoDataResDTO> storeInfo = baseInfoClient.getStoreInfo(merCode, null,
        storeCode);
    Preconditions.checkArgument(storeInfo.checkSuccess(), "查询门店信息失败,暂不支持开票");
    Optional<BizUnitOrgResDTO> first = storeInfo.getData().getBizUnitOrgList().stream()
        .filter(d -> "1".equals(d.getLayer())).findFirst();
    Preconditions.checkArgument(first.isPresent(), "查询门店所属公司信息失败,暂不支持开票");
    invoiceMain.setCompanyCode(first.get().getUnitCode());
    invoiceMain.setCompanyName(first.get().getUnitName());

    return InvoiceConvert.applyInvoiceConvertByOnlineOrderB2C(allDetailRes, refundDataList, command,
        detailListPriceResponse.getData());

  }

}
