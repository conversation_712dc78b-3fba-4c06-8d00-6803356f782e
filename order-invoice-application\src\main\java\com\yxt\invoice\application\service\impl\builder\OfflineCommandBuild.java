package com.yxt.invoice.application.service.impl.builder;

import com.google.common.base.Preconditions;
import com.yxt.invoice.application.service.convert.InvoiceConvert;
import com.yxt.invoice.application.third.goods.dto.req.AveragePriceQuery;
import com.yxt.invoice.application.third.goods.dto.res.AveragePriceVO;
import com.yxt.invoice.application.third.goods.feign.MiddleMerchandiseClient;
import com.yxt.invoice.application.third.order.feign.OfflineOrderManageQueryApiFeign;
import com.yxt.invoice.application.third.order.feign.OfflineOrderQueryApiFeign;
import com.yxt.invoice.domain.command.ApplyInvoiceCommand;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.open.message.offline_order.model.OfflineOrderModel;
import com.yxt.order.open.message.offline_order.model.OfflineRefundOrderModel;
import com.yxt.order.open.sdk.offline_order.req.OfflineOrderDetailQueryReqDto;
import com.yxt.order.open.sdk.offline_order.req.OfflineRefundOrderDetailQueryReqDto;
import com.yxt.order.open.sdk.offline_order.req.manage.IOfflineRefundOrderListReq;
import com.yxt.order.open.sdk.offline_order.res.manage.IOfflineRefundOrderRes;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.invoice.enums.InvoiceTransactionChannelEnum;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class OfflineCommandBuild extends AbstractApplyInvoiceCommandBuilder {


  @Resource
  private OfflineOrderManageQueryApiFeign offlineOrderManageQueryApiFeign;

  @Resource
  private OfflineOrderQueryApiFeign offlineOrderQueryApiFeign;

  @Resource
  private MiddleMerchandiseClient middleMerchandiseClient;


  @Override
  public Boolean route(ApplyInvoiceCommand command) {
    return command.getInvoiceMain().getTransactionChannel()
        .equals(InvoiceTransactionChannelEnum.OFFLINE.name());
  }

  @Override
  public ApplyInvoiceCommand build(ApplyInvoiceCommand command) {
    return extractedByOffline(command);
  }


  private ApplyInvoiceCommand extractedByOffline(ApplyInvoiceCommand command) {

    InvoiceMain invoiceMain = command.getInvoiceMain();
    String orderNo = invoiceMain.getOrderNo().getOrderNo();
    String userId = invoiceMain.getUserId();
    String merCode = invoiceMain.getMerCode();

    OfflineOrderDetailQueryReqDto queryReqDto = new OfflineOrderDetailQueryReqDto();
    queryReqDto.setOrderNo(orderNo);
    //查询线下单补充
    ResponseBase<OfflineOrderModel> offlineOrderModelResponseBase = offlineOrderQueryApiFeign.detail(
        queryReqDto);
    Preconditions.checkArgument(offlineOrderModelResponseBase.checkSuccess(),
        "查询线下单数据异常,暂不支持开票");

    OfflineOrderModel data = offlineOrderModelResponseBase.getData();
    if (null != data.getBaseUserInfo().getUserId() && StringUtils.isNotEmpty(userId)) {
      Preconditions.checkArgument(data.getBaseUserInfo().getUserId().getUserId().equals(userId),
          "此订单为其他会员所有,请核对信息申请");
    }
    IOfflineRefundOrderListReq offlineRefundOrderListReq = new IOfflineRefundOrderListReq();
    offlineRefundOrderListReq.setOrderNo(orderNo);
    ResponseBase<PageDTO<IOfflineRefundOrderRes>> pageDTOResponseBase = offlineOrderManageQueryApiFeign.offlineRefundOrderList(
        offlineRefundOrderListReq);
    Preconditions.checkArgument(pageDTOResponseBase.checkSuccess(),
        "查询线下单退单数据异常,暂不支持开票");

    List<IOfflineRefundOrderRes> refundDataTempList = pageDTOResponseBase.getData().getData();
    List<OfflineRefundOrderModel> refundDataList = new ArrayList<>();

    for (IOfflineRefundOrderRes iOfflineRefundOrderRes : refundDataTempList) {
      OfflineRefundOrderDetailQueryReqDto refundDetailQueryReqDto = new OfflineRefundOrderDetailQueryReqDto();
      refundDetailQueryReqDto.setRefundNo(iOfflineRefundOrderRes.getRefundNo());
      ResponseBase<OfflineRefundOrderModel> offlineRefundOrderModelResponseBase = offlineOrderQueryApiFeign.refundDetail(
          refundDetailQueryReqDto);
      Preconditions.checkArgument(offlineRefundOrderModelResponseBase.checkSuccess(),
          "查询线下单退单数据异常,暂不支持开票");
      Preconditions.checkArgument(
          !offlineRefundOrderModelResponseBase.getData().getBaseRefundInfo().getRefundTypeValue()
              .equals("ALL"), "暂不支持整单退开票");
      refundDataList.add(offlineRefundOrderModelResponseBase.getData());
    }
    String storeCode = data.getBaseOrganizationInfo().getStoreCode();
    List<String> erpCodeList = data.getOrderDetailList().stream()
        .map(d -> d.getBaseOrderDetailInfo().getErpCode().getErpCode()).distinct()
        .collect(Collectors.toList());
    ResponseBase<List<AveragePriceVO>> detailListPriceResponse = middleMerchandiseClient.queryAveragePrice(
        DsConstants.SYSTEM, AveragePriceQuery.buildBean(merCode, storeCode, erpCodeList));
    Preconditions.checkArgument(detailListPriceResponse.checkSuccess(),
        "查询商品平均价失败,暂不支持开票");

    return InvoiceConvert.applyInvoiceConvertByOfflineOrder(data, refundDataList, command,
        detailListPriceResponse.getData());
  }


}
