package com.yxt.invoice.domain.event.create;

import com.yxt.invoice.domain.event.BaseCreateInvoiceDomainEvent;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 拉回发票信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
public class PullBackInvoiceInfoEvent extends BaseCreateInvoiceDomainEvent<PullBackInvoiceInfoEvent.Data> {

    public static final String TYPE = "InvoiceCheckProviderPullEvent";

    public PullBackInvoiceInfoEvent(InvoiceAggregate aggregate) {
        super(aggregate, null, TYPE,new Data(aggregate));
    }

    @Getter
    @Setter
    @ToString(callSuper = true)
    @NoArgsConstructor
    public static class Data extends BaseCreateInvoiceDomainEvent.BaseData {
        private InvoiceAggregate invoiceAggregate;

        Data(InvoiceAggregate aggregate) {
            super.convert(aggregate);
            this.invoiceAggregate = aggregate;
        }
    }
}
